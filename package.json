{"name": "sales-flow-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:docker": "mv next.config.js next.config.js.bak && mv next.config.docker.js next.config.js && next build && mv next.config.js next.config.docker.js && mv next.config.js.bak next.config.js", "start": "next start", "lint": "next lint"}, "dependencies": {"@hapi/boom": "^10.0.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@whiskeysockets/baileys": "^6.7.17", "jimp": "^0.16.13", "link-preview-js": "^3.0.15", "next": "^15.3.2", "next-auth": "^4.24.11", "openai": "^4.103.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.2", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "eslint": "^9", "eslint-config-next": "15.1.8", "playwright": "^1.52.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}