# Multi-stage Docker build for Next.js application
# Optimized for production deployment on Render with enhanced caching and security

# Stage 1: Dependencies
FROM node:23-slim AS deps
WORKDIR /app

# Install system dependencies with better caching
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6-dev \
  ca-certificates \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Copy package files for better layer caching
COPY package.json package-lock.json* ./

# Install production dependencies only with better caching
RUN --mount=type=cache,target=/root/.npm \
  npm ci --omit=dev --legacy-peer-deps --no-audit --no-fund

# Stage 2: Builder
FROM node:23-slim AS builder
WORKDIR /app

# Install build dependencies for native modules with better caching
RUN apt-get update && apt-get install -y --no-install-recommends \
  build-essential \
  ca-certificates \
  g++ \
  make \
  python3 \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Copy package files for full install
COPY package.json package-lock.json* ./

# Install ALL dependencies (including dev) for build with caching
RUN --mount=type=cache,target=/root/.npm \
  npm ci --legacy-peer-deps --no-audit --no-fund

# Copy source code (excluding unnecessary files)
COPY . .
RUN rm -rf node_modules/.cache 2>/dev/null || true

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1 \
  NODE_ENV=production \
  ESLINT_NO_DEV_ERRORS=true \
  UV_THREADPOOL_SIZE=128 \
  NODE_OPTIONS="--max-old-space-size=2048"

# Minimal environment variables required for build (will be overridden at runtime)
ENV NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co \
  NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder_anon_key \
  SUPABASE_URL=https://placeholder.supabase.co \
  SUPABASE_SERVICE_ROLE_KEY=placeholder_service_key \
  NEXTAUTH_SECRET=placeholder_secret_for_build_only \
  NEXTAUTH_URL=https://placeholder.example.com \
  OPENAI_API_KEY=placeholder_openai_key \
  GOOGLE_CLIENT_ID=placeholder_google_client_id \
  GOOGLE_CLIENT_SECRET=placeholder_google_client_secret \
  ALLOWED_EMAILS=<EMAIL>

# Build the application (skip linting for Docker builds)
RUN npm run build:docker

# Verify build output
RUN ls -la .next/ && \
  test -f .next/standalone/server.js || (echo "Build failed: server.js not found" && exit 1)

# Stage 3: Runner
FROM node:23-slim AS runner
WORKDIR /app

# Install runtime dependencies for native modules with better security
RUN apt-get update && apt-get install -y --no-install-recommends \
  ca-certificates \
  libc6-dev \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Set production environment variables
ENV NODE_ENV=production \
  NEXT_TELEMETRY_DISABLED=1 \
  UV_THREADPOOL_SIZE=128 \
  NODE_OPTIONS="--max-old-space-size=2048" \
  PORT=3000 \
  HOSTNAME="0.0.0.0"

# Create non-root user for security with proper permissions
RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 --gid 1001 nextjs

# Copy built application with proper ownership
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Create and set permissions for Next.js cache directory
RUN mkdir -p .next && \
  chown nextjs:nodejs .next

# Copy built application with correct permissions
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Create directories for application data with proper permissions
RUN mkdir -p /app/whatsapp-sessions /app/logs /app/tmp && \
  chown -R nextjs:nodejs /app/whatsapp-sessions /app/logs /app/tmp && \
  chmod 755 /app/whatsapp-sessions /app/logs /app/tmp

# Copy and set permissions for healthcheck script
COPY --chown=nextjs:nodejs healthcheck.js ./
RUN chmod +x healthcheck.js

# Switch to non-root user for security
USER nextjs

# Expose application ports
EXPOSE 3000 3002

# Enhanced health check with better timing
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
  CMD node healthcheck.js || exit 1

# Start the application with proper error handling
CMD ["node", "server.js"]
