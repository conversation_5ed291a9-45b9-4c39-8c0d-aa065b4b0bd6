# Multi-stage Docker build for Next.js application
# Optimized for production deployment on Render

# Stage 1: Dependencies
FROM node:23-slim AS deps
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6-dev \
  && rm -rf /var/lib/apt/lists/*
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install production dependencies only
RUN npm ci --omit=dev --legacy-peer-deps

# Stage 2: Builder
FROM node:23-slim AS builder
WORKDIR /app

# Install build dependencies for native modules (bufferutil, utf-8-validate)
RUN apt-get update && apt-get install -y --no-install-recommends \
  build-essential \
  g++ \
  make \
  python3 \
  && rm -rf /var/lib/apt/lists/*

# Copy package files for full install
COPY package.json package-lock.json* ./

# Install ALL dependencies (including dev) for build
RUN npm ci --legacy-peer-deps

# Copy source code
COPY . .

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production
ENV ESLINT_NO_DEV_ERRORS true
# WebSocket configuration for Docker
ENV UV_THREADPOOL_SIZE 128
ENV NODE_OPTIONS "--max-old-space-size=2048"

# Minimal environment variables required for build (will be overridden at runtime)
ENV NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder_anon_key
ENV SUPABASE_URL=https://placeholder.supabase.co
ENV SUPABASE_SERVICE_ROLE_KEY=placeholder_service_key
ENV NEXTAUTH_SECRET=placeholder_secret_for_build_only
ENV NEXTAUTH_URL=https://placeholder.example.com
ENV OPENAI_API_KEY=placeholder_openai_key
ENV GOOGLE_CLIENT_ID=placeholder_google_client_id
ENV GOOGLE_CLIENT_SECRET=placeholder_google_client_secret
ENV ALLOWED_EMAILS=<EMAIL>

# Build the application (skip linting for Docker builds)
RUN npm run build:docker

# Stage 3: Runner
FROM node:23-slim AS runner
WORKDIR /app

# Install runtime dependencies for native modules
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6-dev \
  && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next && \
  chown nextjs:nodejs .next

# Copy built application with correct permissions
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Create directories for WhatsApp sessions and logs
RUN mkdir -p /app/whatsapp-sessions /app/logs && \
  chown -R nextjs:nodejs /app/whatsapp-sessions /app/logs

# Switch to non-root user
USER nextjs

# Expose ports
EXPOSE 3000
EXPOSE 3002

# Set port environment variable
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"
# WebSocket configuration for Docker
ENV UV_THREADPOOL_SIZE 128
ENV NODE_OPTIONS "--max-old-space-size=2048"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Start the application
CMD ["node", "server.js"]
