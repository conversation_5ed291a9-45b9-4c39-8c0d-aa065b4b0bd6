# Docker Compose for local development and testing
version: '3.8'

services:
  # Main application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-here-for-local-testing
      - WHATSAPP_SESSION_PATH=/app/whatsapp-sessions
      - PORT=3000
      - HOSTNAME=0.0.0.0
    volumes:
      # Mount WhatsApp sessions for persistence
      - whatsapp_sessions:/app/whatsapp-sessions
      # Mount logs for debugging
      - app_logs:/app/logs
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    depends_on:
      - redis
    networks:
      - salesflow-network

  # Redis for caching and session storage (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - salesflow-network

  # PostgreSQL for local development (optional)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=salesflow_ai
      - POSTGRES_USER=salesflow_ai_user
      - POSTGRES_PASSWORD=local_dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - salesflow-network

volumes:
  whatsapp_sessions:
    driver: local
  app_logs:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  salesflow-network:
    driver: bridge
