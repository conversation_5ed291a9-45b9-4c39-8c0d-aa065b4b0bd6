# Product Requirements Document (PRD)

## Product Name: SalesFlow AI Builder

---

## Overview

SalesFlow AI Builder is a wizard-based server application built with **Next.js**, designed to help businesses create AI-powered WhatsApp sales assistants. It enables users to configure assistant behavior, store product data, and test AI responses using **OpenAI** LLMs. All data — including user configuration, transactional logs, and vector embeddings — is managed using **Supabase** (PostgreSQL + pgvector). The app is deployed via **Docker** on **Render**, a modern PaaS platform that provides automatic HTTPS and continuous deployment from Git.

---

## Core Features

### 1. Wizard-Based Assistant Builder

**Simplified 4-Step Flow** with Quick Start option:

**Step 0: Quick Start Selection**

- 🚀 Sample Data Option: Pre-configured assistants for immediate testing
- ✨ Custom Setup: Traditional step-by-step configuration

**Main Wizard Steps:**

1. 🏢 Business Setup - Company info & industry
2. 🤖 Sales Assistant - AI personality & behavior
3. 📦 Product Knowledge - Products & services
4. 🚀 Go Live - WhatsApp connection & testing

**Quick Start Sample Agents:**

- 👗 Fashion E-commerce Assistant (StyleHub Boutique)
- 💻 SaaS Productivity Tool Assistant (TaskFlow Pro)
- 🍽️ Local Restaurant Assistant (Bella Vista Italian Kitchen)

### 2. RAG-based Product Info

- Product data stored in Supabase
- Embedded using Supabase's built-in embedding capabilities
- Queried using pgvector for semantic search

### 3. Multi-LLM Model Support

- Start with OpenAI
- Future support for Gemini, Claude, etc.

### 4. WhatsApp Integration

- Uses Baileys for QR-based login and session handling
- Bi-directional message support

### 5. Testing Mode

- Simulated UI for previewing AI assistant behavior
- Logs test outcomes and flagged issues

### 6. Security & Access Control

**Authentication & Authorization:**

- OAuth-based Google sign-in using `next-auth`
- Email whitelist access control (individual emails + domain-based)
- JWT-based sessions with 30-day expiry
- Protected routes with middleware-based authentication

**Data Security:**

- Row-Level Security (RLS) on all database tables
- User data isolation (users can only access their own data)
- Encrypted credentials storage for WhatsApp sessions
- Secure environment variable management

**Admin & User Management:**

- Admin dashboard for user management
- Role-based access control (Admin, User, Viewer)
- User status management (Active, Inactive, Suspended)
- Bulk user operations and access revocation

**Audit & Monitoring:**

- Comprehensive security event logging
- Authentication attempt tracking (success/failure)
- API usage monitoring and rate limiting
- Suspicious activity detection and alerts

---

## Tech Stack

| Layer         | Technology                       |
| ------------- | -------------------------------- |
| App Framework | Next.js (App Router)             |
| API Routes    | Next.js API routes               |
| Auth          | Google via `next-auth`           |
| Data Store    | Supabase (PostgreSQL + pgvector) |
| Messaging     | Baileys (WhatsApp)               |
| LLM           | OpenAI (future: Gemini, Claude)  |
| Hosting       | Render (Docker deployment)       |

---

## Pages & Routes

| Route             | Description                        |
| ----------------- | ---------------------------------- |
| `/login`          | Google Sign-In                     |
| `/builder`        | Assistant setup wizard             |
| `/preview`        | Testing mode                       |
| `/admin`          | Admin dashboard (admin-only)       |
| `/admin/users`    | User management (admin-only)       |
| `/admin/security` | Security monitoring (admin-only)   |
| `/api/assistant`  | CRUD for assistant configs         |
| `/api/whatsapp`   | Message handling                   |
| `/api/llm`        | Proxy endpoint for LLM interaction |
| `/api/products`   | Vector search API                  |
| `/api/admin`      | Admin operations API               |

---

## Security Features Checklist

### ✅ **Implemented Security Features**

#### Authentication & Access Control

- ✅ Google OAuth integration with NextAuth.js
- ✅ Email whitelist system (ALLOWED_EMAILS environment variable)
- ✅ JWT-based session management (30-day expiry)
- ✅ Protected routes via NextAuth middleware
- ✅ Fail-secure default (no access if whitelist not configured)
- ✅ Comprehensive API protection via middleware

#### Data Security & Isolation

- ✅ Row-Level Security (RLS) on all database tables
- ✅ User data isolation (users can only access own data)
- ✅ Server-side session validation on all API endpoints
- ✅ User ownership checks on all CRUD operations
- ✅ Encrypted WhatsApp credentials storage
- ✅ Secure environment variable management

#### Basic Audit & Logging

- ✅ Transaction logs table with metadata
- ✅ LLM usage tracking (tokens, costs, models)
- ✅ WhatsApp message logging
- ✅ Assistant activity logging
- ✅ Authentication attempt logging (console)

### 🔄 **In Progress Security Features**

#### Enhanced Access Control

- 🔄 Environment-based admin user designation
- 🔄 Admin dashboard for user management
- 🔄 Email whitelist management interface

### 📋 **Planned Security Features**

#### Role-Based Access Control (RBAC)

- 📋 User roles table (Admin, User, Viewer)
- 📋 Role-based permissions system
- 📋 Granular feature access control
- 📋 Role assignment and management

#### Advanced User Management

- 📋 User status management (Active, Inactive, Suspended)
- 📋 Bulk user operations
- 📋 User access revocation
- 📋 Session management (force logout)
- 📋 User activity monitoring

#### Security Monitoring & Audit

- 📋 Centralized security event logging
- 📋 Failed login attempt tracking
- 📋 Suspicious activity detection
- 📋 Security dashboard with metrics
- 📋 Real-time security alerts
- 📋 Audit trail for admin actions

#### Advanced Security Features

- 📋 API rate limiting
- 📋 IP-based access restrictions
- 📋 Device/location tracking
- 📋 Multi-factor authentication (MFA)
- 📋 Session timeout controls
- 📋 Password policy enforcement (if applicable)

---

## Task List

| #   | Task                                                     | Priority | Status  |
| --- | -------------------------------------------------------- | -------- | ------- |
| 1   | Setup Next.js project                                    | 🔥 High  | ✅ Done |
| 2   | Configure Google Login via `next-auth`                   | 🔥 High  | ✅ Done |
| 3   | Integrate Supabase and define schema                     | 🔥 High  | ✅ Done |
| 4   | Build wizard UI with Quick Start sample data             | 🔥 High  | ✅ Done |
| 5   | Implement email whitelist security                       | 🔥 High  | ✅ Done |
| 6   | Setup Row-Level Security (RLS) policies                  | 🔥 High  | ✅ Done |
| 7   | Integrate OpenAI API                                     | 🔥 High  | 🔄 Next |
| 8   | Environment-based admin user designation                 | 🔥 High  | 🔄 Next |
| 9   | Admin dashboard for user management                      | 🔥 High  | 📋 Todo |
| 10  | Add WhatsApp QR login via Baileys                        | 🔥 High  | 📋 Todo |
| 11  | Testing mode chat UI                                     | ✅ Med   | 📋 Todo |
| 12  | Product RAG System - Supabase embeddings & vector search | ✅ Med   | 📋 Todo |
| 13  | Role-based access control (RBAC)                         | ✅ Med   | 📋 Todo |
| 14  | Security monitoring dashboard                            | ✅ Med   | 📋 Todo |
| 15  | API rate limiting and security hardening                 | ✅ Med   | 📋 Todo |
| 16  | Optional: Google Sheets sync for product data            | ✅ Low   | 📋 Todo |
| 17  | Configure Render deployment with Dockerfile              | 🔥 High  | 📋 Todo |
| 18  | Setup environment variables and custom domain on Render  | 🔥 High  | 📋 Todo |

---

## Development Phases

### MVP

- ✅ Google Login (OAuth via next-auth)
- ✅ Email whitelist access control
- ✅ Row-Level Security (RLS) implementation
- ✅ Simplified 4-step Wizard UI with Quick Start sample data
- ✅ Supabase integration for data, embeddings, and vector storage
- 🔄 Environment-based admin user designation
- 🔄 OpenAI model for chat completions
- 📋 Admin dashboard for user management
- 📋 WhatsApp integration (Baileys QR-based login)
- 📋 Testing mode with product-based RAG using Supabase embeddings
- 📋 Docker deployment via Render with auto HTTPS

**Quick Start Sample Agents (✅ Completed):**

- Fashion E-commerce (StyleHub Boutique) - 4 products, styling consultation
- SaaS Productivity (TaskFlow Pro) - 4 plans, lead qualification
- Restaurant (Bella Vista) - 4 menu items, order taking & reservations

### Security Phase 1 (Current Sprint)

- 🔄 Environment-based admin user designation
- 📋 Admin dashboard for user management
- 📋 Email whitelist management interface
- 📋 User list and status management
- 📋 Basic security event logging

### Security Phase 2 (Next Sprint)

- 📋 Role-based access control (RBAC)
- 📋 Advanced user management features
- 📋 Security monitoring dashboard
- 📋 API rate limiting
- 📋 Enhanced audit logging

### Future Enhancements

- Support Gemini and Claude
- Google Sheets import and auto-embedding
- Assistant usage analytics
- LLM cost metering per WhatsApp user
- AI handoff to human operators
- Assistant templates and prompt versioning
- Multi-factor authentication (MFA)
- Advanced security monitoring and alerts

---

## Security Architecture

### Authentication Flow

```mermaid
graph TD
  A[User Login Request] --> B{Email in Whitelist?}
  B -->|No| C[Access Denied]
  B -->|Yes| D[Google OAuth]
  D --> E[JWT Token Generated]
  E --> F[Session Created]
  F --> G[Access Granted]

  C --> H[Log Security Event]
  G --> I[Log Successful Login]
```

### Data Access Control

```mermaid
graph TD
  A[API Request] --> B{Valid JWT?}
  B -->|No| C[401 Unauthorized]
  B -->|Yes| D{User Owns Resource?}
  D -->|No| E[403 Forbidden]
  D -->|Yes| F[RLS Policy Check]
  F --> G[Data Access Granted]

  C --> H[Log Failed Auth]
  E --> I[Log Access Violation]
  G --> J[Log Data Access]
```

### Admin Access Control

```mermaid
graph TD
  A[Admin Request] --> B{Valid JWT?}
  B -->|No| C[401 Unauthorized]
  B -->|Yes| D{User is Admin?}
  D -->|No| E[403 Forbidden]
  D -->|Yes| F[Admin Action Allowed]
  F --> G[Log Admin Action]
```

---

## Mermaid Diagram (Unified Server Flow)

```mermaid
graph TD
  A1[Login Page - /login]
  A2[Wizard Setup - /builder]
  A3[Testing Mode - /preview]
  A4[Admin Dashboard - /admin]

  B1[Google Auth - next-auth]
  B2[Assistant API - /api/assistant]
  B3[LLM Proxy API - /api/llm]
  B4[WhatsApp API - /api/whatsapp]
  B5[Product Search API - /api/products]
  B6[Admin API - /api/admin]

  C1[Supabase - Config, Vectors, Transactions]
  C2[OpenAI API]
  C3[Baileys - WhatsApp Personal API]
  C4[Security Logs & Audit]

  A1 --> B1
  A2 --> B2
  A2 --> B3
  A2 --> B4
  A2 --> B5
  A3 --> B3
  A4 --> B6

  B1 --> C1
  B1 --> C4
  B2 --> C1
  B3 --> C2
  B4 --> C3
  B5 --> C1
  B6 --> C1
  B6 --> C4
```
