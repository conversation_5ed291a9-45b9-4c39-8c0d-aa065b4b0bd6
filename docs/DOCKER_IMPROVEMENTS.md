# Docker Improvements and Testing Guide

This document outlines the improvements made to the Docker configuration and testing infrastructure for SalesFlow AI.

## 🚀 Dockerfile Improvements

### Enhanced Multi-Stage Build

The Dockerfile has been optimized with the following improvements:

#### 1. Better Caching Strategy

- **Build cache mounts**: Uses `--mount=type=cache,target=/root/.npm` for faster builds
- **Layer optimization**: Improved layer ordering for better cache utilization
- **Package sorting**: Alphabetically sorted packages for consistent builds

#### 2. Enhanced Security

- **Non-root user**: Application runs as `nextjs` user (UID 1001)
- **Proper permissions**: Correct file ownership and permissions
- **Minimal attack surface**: Only necessary packages installed
- **Security headers**: Enhanced security configuration

#### 3. Improved Performance

- **Memory optimization**: Configured Node.js memory settings
- **WebSocket support**: Enhanced WebSocket configuration for Docker
- **Build verification**: Automatic build output verification
- **Resource limits**: Proper resource allocation

#### 4. Better Error Handling

- **Enhanced health checks**: Improved health check timing and retries
- **Build validation**: Verification of build artifacts
- **Comprehensive logging**: Better error reporting and diagnostics

### Key Changes Made

```dockerfile
# Before: Basic npm install
RUN npm ci --omit=dev --legacy-peer-deps

# After: Optimized with caching
RUN --mount=type=cache,target=/root/.npm \
  npm ci --omit=dev --legacy-peer-deps --no-audit --no-fund
```

```dockerfile
# Before: Basic environment variables
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# After: Consolidated and optimized
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    UV_THREADPOOL_SIZE=128 \
    NODE_OPTIONS="--max-old-space-size=2048"
```

## 🧪 Improved Testing Infrastructure

### Enhanced Test Script (`docker-test.sh`)

The existing test script has been improved to provide comprehensive testing:

#### Features:

- **Image validation**: Verifies Docker image exists and shows metadata
- **Environment setup**: Uses existing `.env.local` file directly
- **Health monitoring**: Tests application health endpoint
- **Multi-endpoint testing**: Tests main application routes
- **Colorful output**: Clear, structured output with progress indicators
- **Automatic cleanup**: Proper container cleanup on exit

#### Usage:

```bash
# Run tests with latest image
./scripts/docker-test.sh

# Test specific image tag
./scripts/docker-test.sh v1.2.3
```

#### Test Process:

1. **Image Availability**: Checks if Docker image exists
2. **Environment Check**: Validates `.env.local` file exists
3. **Container Startup**: Starts container with test port override
4. **Health Check**: Tests `/api/health` endpoint
5. **Endpoint Testing**: Tests multiple application endpoints
6. **Results Display**: Shows container info and test URLs

### Enhanced Health Check (`healthcheck.js`)

Improved health check script with:

#### Features:

- **Retry logic**: Configurable retry attempts with delays
- **Detailed logging**: Timestamped logs with structured output
- **JSON parsing**: Proper health response parsing and validation
- **Timeout handling**: Configurable timeout with proper cleanup
- **Error reporting**: Comprehensive error messages and diagnostics

#### Configuration:

```javascript
const config = {
  host: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/health',
  timeout: 5000,
  maxRetries: 3,
  retryDelay: 1000,
};
```

## 📊 Testing Workflow

### Complete Testing Process

1. **Build the Docker image**:

   ```bash
   ./scripts/docker-build.sh
   ```

2. **Run comprehensive tests**:

   ```bash
   ./scripts/docker-test.sh
   ```

3. **Review results**:
   - Check console output for test results
   - Verify all endpoints are working
   - Monitor container health and performance

### Test Output Examples

#### Test Output:

```
🧪 Testing Docker image: salesflow-ai:latest
Container: salesflow-ai-test
Test Port: 3001

🔍 Testing Docker Image Availability
==================================================
✅ Image found: salesflow-ai:latest
ℹ️  Image ID: fe331cfbfe5b
ℹ️  Image Size: 356MB

🔍 Checking Environment Configuration
==================================================
✅ Using existing .env.local file
ℹ️  Environment file found and will be used for testing

🔍 Starting Docker Container
==================================================
✅ Container started successfully

🔍 Waiting for Container to be Ready
==================================================
✅ Container is healthy and ready

🔍 Testing Health Endpoint
==================================================
✅ Health endpoint responding correctly (HTTP 200)

🔍 Testing Main Application Endpoints
==================================================
✅ Endpoint /: HTTP 200
✅ Endpoint /login: HTTP 200
✅ Endpoint /unauthorized: HTTP 200

✅ All tests completed successfully!
🎉 Docker container is working correctly!
```

## 🔧 Configuration Options

### Environment Variables

The Docker container supports the following key environment variables:

```bash
# Application
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# Performance
UV_THREADPOOL_SIZE=128
NODE_OPTIONS="--max-old-space-size=2048"

# Next.js
NEXT_TELEMETRY_DISABLED=1

# Application-specific (see .env.local)
NEXTAUTH_URL=http://localhost:3000
SUPABASE_URL=your_supabase_url
OPENAI_API_KEY=your_openai_key
# ... other environment variables
```

### Docker Build Arguments

```bash
# Build with specific Node.js version
docker build --build-arg NODE_VERSION=23 .

# Build with custom cache mount
docker build --mount=type=cache,target=/root/.npm .
```

## 🚨 Troubleshooting

### Common Issues and Solutions

1. **Health check failures**:

   - Check if the application is properly started
   - Verify environment variables are set correctly
   - Review container logs: `docker logs container-name`

2. **Performance issues**:

   - Monitor resource usage with load testing script
   - Check memory limits and Node.js heap size
   - Review application logs for errors

3. **Build failures**:

   - Ensure all dependencies are available
   - Check network connectivity for package downloads
   - Verify Docker build context size

4. **Permission issues**:
   - Verify the application runs as non-root user
   - Check file permissions in container
   - Review volume mount permissions

### Debug Commands

```bash
# Check container status
docker ps -a

# View container logs
docker logs container-name

# Execute commands in container
docker exec -it container-name /bin/bash

# Inspect container configuration
docker inspect container-name

# Monitor resource usage
docker stats container-name
```

## 📈 Performance Benchmarks

### Expected Performance Metrics

- **Startup time**: < 15 seconds
- **Health check response**: < 100ms
- **Memory usage**: < 512MB under normal load
- **CPU usage**: < 50% under normal load
- **Success rate**: > 99% for health checks

### Monitoring and Alerts

The testing scripts generate metrics that can be used for:

- Performance regression detection
- Resource usage monitoring
- Error rate tracking
- Capacity planning

## 🔄 Continuous Integration

### Integration with CI/CD

The testing scripts can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Test Docker Image
  run: |
    ./scripts/docker-build.sh
    ./scripts/docker-test.sh
```

### Automated Testing

- **Pre-deployment**: Run comprehensive tests before deployment
- **Post-deployment**: Verify deployment with health checks
- **Monitoring**: Continuous health monitoring in production

This improved Docker configuration and testing infrastructure provides a robust foundation for deploying SalesFlow AI in production environments with confidence in performance, security, and reliability.
