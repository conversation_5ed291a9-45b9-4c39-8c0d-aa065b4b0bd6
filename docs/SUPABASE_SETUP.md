# Supabase Setup Guide

This guide will help you set up Supabase for the SalesFlow AI project with PostgreSQL and pgvector for vector operations.

## 📋 Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Node.js and npm installed
- Git repository cloned locally

## 🚀 Step 1: Create Supabase Project

1. **Sign in to Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Sign in with your account

2. **Create New Project**
   - Click "New Project"
   - Choose your organization
   - Enter project details:
     - **Name**: `sales-flow-ai-dev` (or your preferred name)
     - **Database Password**: Generate a strong password
     - **Region**: Choose `ap-southeast-1` (Singapore) or closest to your location
   - Click "Create new project"

3. **Wait for Project Setup**
   - Project creation takes 2-3 minutes
   - You'll see a progress indicator

## 🔧 Step 2: Configure Environment Variables

1. **Get Project Credentials**
   - In your Supabase dashboard, go to **Settings** → **API**
   - Copy the following values:
     - **Project URL** (e.g., `https://your-project-id.supabase.co`)
     - **<PERSON><PERSON> (public) key**
     - **Service role (secret) key**

2. **Update Environment File**
   - Copy `.env.example` to `.env.local`:
     ```bash
     cp .env.example .env.local
     ```
   - Update the Supabase configuration in `.env.local`:
     ```env
     # Supabase Configuration
     NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
     SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
     ```

## 🗄️ Step 3: Enable pgvector Extension

1. **Access SQL Editor**
   - In Supabase dashboard, go to **SQL Editor**
   - Click "New query"

2. **Enable pgvector**
   ```sql
   CREATE EXTENSION IF NOT EXISTS vector;
   ```
   - Click "Run" to execute

## 📊 Step 4: Run Database Migrations

Run the migration scripts in order to set up the database schema:

### Migration 1: Initial Schema
```sql
-- Copy and paste the contents of src/lib/supabase/migrations/001_initial_schema.sql
-- This creates all tables, indexes, and triggers
```

### Migration 2: Row Level Security
```sql
-- Copy and paste the contents of src/lib/supabase/migrations/002_rls_policies.sql
-- This sets up security policies for data isolation
```

### Migration 3: Vector Functions
```sql
-- Copy and paste the contents of src/lib/supabase/migrations/003_vector_functions.sql
-- This creates vector similarity search functions
```

### Alternative: Run All Migrations at Once
You can copy and paste all three migration files in sequence, or run them individually.

## ✅ Step 5: Verify Setup

1. **Check Tables Created**
   - Go to **Database** → **Tables**
   - You should see 5 tables:
     - `user_profiles`
     - `assistant_configurations`
     - `products`
     - `transaction_logs`
     - `whatsapp_sessions`

2. **Verify Extensions**
   - Go to **Database** → **Extensions**
   - Confirm `vector` extension is enabled

3. **Test Connection**
   - Start your development server:
     ```bash
     npm run dev
     ```
   - Visit: `http://localhost:3000/test-db`
   - Sign in and click "Run Database Test"
   - Should show successful connection

## 🔒 Security Configuration

### Row Level Security (RLS)
All tables have RLS enabled with policies that ensure:
- Users can only access their own data
- Proper authentication is required
- Service role has appropriate permissions

### Environment Security
- Never commit `.env.local` to version control
- Use different projects for development/staging/production
- Rotate keys regularly in production

## 📋 Database Schema Overview

### Tables Created:
- **user_profiles**: User data and preferences
- **assistant_configurations**: AI assistant settings
- **products**: Product data with vector embeddings (1536 dimensions)
- **transaction_logs**: Activity tracking and audit trails
- **whatsapp_sessions**: WhatsApp connection management

### Key Features:
- **Vector Search**: pgvector with similarity functions
- **Row Level Security**: Complete data isolation
- **Performance**: Comprehensive indexing
- **Automation**: Automatic timestamp updates

## 🛠️ Troubleshooting

### Common Issues:

1. **Connection Errors**
   - Verify environment variables are correct
   - Check project URL format
   - Ensure keys are properly copied

2. **Migration Errors**
   - Run migrations in correct order
   - Check for syntax errors
   - Verify pgvector extension is enabled

3. **Permission Errors**
   - Ensure RLS policies are applied
   - Check user authentication
   - Verify service role permissions

### Getting Help:
- Check Supabase documentation: [docs.supabase.com](https://docs.supabase.com)
- Review migration files for schema details
- Test connection using the `/test-db` endpoint

## 🚀 Next Steps

Once Supabase is set up:
1. Test the database connection
2. Verify all tables and functions work
3. Start building the wizard UI (Task 4)
4. Integrate OpenAI API for embeddings (Task 5)

Your Supabase integration is now ready for the SalesFlow AI application! 🎉
