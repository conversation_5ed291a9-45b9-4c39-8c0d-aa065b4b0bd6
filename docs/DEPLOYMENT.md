# Deployment Guide

This guide covers Docker containerization and deployment to Render for the SalesFlow AI application.

## 🐳 Docker Setup

### Prerequisites

- Docker installed on your system
- Docker Compose (optional, for local development)

### Quick Start

1. **Build the Docker image:**
   ```bash
   ./scripts/docker-build.sh
   ```

2. **Test the container locally:**
   ```bash
   ./scripts/docker-test.sh
   ```

3. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

### Docker Configuration

#### Dockerfile Features

- **Multi-stage build** for optimized production images
- **Non-root user** for security
- **Health checks** for monitoring
- **Standalone Next.js output** for minimal runtime
- **Alpine Linux** for smaller image size

#### Environment Variables

Required environment variables for Docker:

```env
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secret-here
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
ALLOWED_EMAILS=<EMAIL>,<EMAIL>
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-key
OPENAI_API_KEY=your-openai-api-key
WHATSAPP_SESSION_PATH=/app/whatsapp-sessions
PORT=3000
HOSTNAME=0.0.0.0
```

## 🚀 Render Deployment

### Prerequisites

- GitHub repository with your code
- Render account (free tier available)
- Environment variables configured

### Deployment Steps

1. **Validate your setup:**
   ```bash
   ./scripts/render-deploy.sh validate
   ```

2. **Review the checklist:**
   ```bash
   ./scripts/render-deploy.sh checklist
   ```

3. **Generate secrets:**
   ```bash
   ./scripts/render-deploy.sh secret
   ```

4. **Deploy on Render:**
   - Go to [Render Dashboard](https://dashboard.render.com/)
   - Connect your GitHub repository
   - Choose "Web Service"
   - Select Docker environment
   - Configure environment variables

### Render Configuration

The `render.yaml` file includes:

- **Docker-based deployment**
- **Health check endpoint** (`/api/health`)
- **Environment variable placeholders**
- **Persistent disk** for WhatsApp sessions
- **Auto-deploy** from main branch

### Environment Variables Setup

Set these in the Render dashboard:

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXTAUTH_URL` | Your Render app URL | `https://your-app.onrender.com` |
| `NEXTAUTH_SECRET` | Generated secret | Use `./scripts/render-deploy.sh secret` |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | From Google Cloud Console |
| `GOOGLE_CLIENT_SECRET` | Google OAuth secret | From Google Cloud Console |
| `ALLOWED_EMAILS` | Comma-separated email list | `<EMAIL>,<EMAIL>` |

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add authorized redirect URI:
   ```
   https://your-app.onrender.com/api/auth/callback/google
   ```

## 🔍 Health Monitoring

### Health Check Endpoint

The application includes a health check endpoint at `/api/health`:

```json
{
  "status": "healthy",
  "timestamp": "2025-05-27T16:30:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "version": "0.1.0",
  "memory": {
    "used": 128,
    "total": 256
  }
}
```

### Docker Health Check

The Dockerfile includes a health check that runs every 30 seconds:

```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js
```

## 🛠️ Local Development

### Docker Compose Development

For local development with hot reloading:

```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

This includes:
- **Hot reloading** with volume mounts
- **PostgreSQL** for local database
- **Redis** for caching
- **MailHog** for email testing

### Testing the Build

Test your Docker build locally before deploying:

```bash
# Build and test
./scripts/docker-build.sh
./scripts/docker-test.sh

# Or use Docker Compose
docker-compose up --build
```

## 📊 Performance Optimization

### Image Size Optimization

- Multi-stage build reduces final image size
- Alpine Linux base image
- Only production dependencies included
- Static files optimized

### Runtime Optimization

- Standalone Next.js output
- Non-root user for security
- Health checks for monitoring
- Proper signal handling

## 🔧 Troubleshooting

### Common Issues

1. **Build fails:**
   - Check Node.js version compatibility
   - Verify all dependencies are listed in package.json
   - Check for missing environment variables

2. **Health check fails:**
   - Verify the `/api/health` endpoint is accessible
   - Check container logs: `docker logs <container-name>`
   - Ensure port 3000 is properly exposed

3. **Authentication issues:**
   - Verify Google OAuth redirect URIs
   - Check NEXTAUTH_URL matches your domain
   - Ensure NEXTAUTH_SECRET is properly set

### Debugging Commands

```bash
# View container logs
docker logs salesflow-ai-test

# Execute shell in container
docker exec -it salesflow-ai-test sh

# Check health endpoint
curl http://localhost:3000/api/health

# View Render logs
# Use Render dashboard or CLI
```

## 📚 Additional Resources

- [Render Documentation](https://render.com/docs)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [NextAuth.js Deployment](https://next-auth.js.org/deployment)
