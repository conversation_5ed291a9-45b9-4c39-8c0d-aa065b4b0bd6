# SalesFlow AI Builder - Environment Configuration Sample
# Copy this file to .env.local and update with your actual values
# DO NOT commit .env.local to version control

# ============================================================================
# NextAuth Configuration
# ============================================================================
NEXTAUTH_URL=http://localhost:3000
# Generate a secure secret using: openssl rand -base64 32
NEXTAUTH_SECRET=your-nextauth-secret-here

# ============================================================================
# Google OAuth Configuration
# ============================================================================
# Get these from Google Cloud Console: https://console.cloud.google.com/
# 1. Create a new project or select existing
# 2. Enable Google+ API
# 3. Create OAuth 2.0 credentials
# 4. Add authorized redirect URIs: http://localhost:3000/api/auth/callback/google
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# ============================================================================
# Security Configuration - Simple Email Whitelist Control
# ============================================================================
# Comma-separated list of allowed email addresses (ONLY these emails can access)
# Example: <EMAIL>,<EMAIL>,<EMAIL>
ALLOWED_EMAILS=<EMAIL>

# ============================================================================
# Supabase Configuration
# ============================================================================
# Get these from your Supabase project dashboard
# 1. Go to https://supabase.com/dashboard
# 2. Select your project
# 3. Go to Settings → API
# 4. Copy the values below
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# ============================================================================
# OpenAI Configuration
# ============================================================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key

# Optional: OpenAI model configuration
# Available models: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-3.5-turbo
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

# ============================================================================
# WhatsApp/Baileys Configuration
# ============================================================================
# Directory for WhatsApp session storage (relative to project root)
WHATSAPP_SESSION_PATH=./whatsapp-sessions

# Custom browser name that appears in WhatsApp linked devices
WHATSAPP_BROWSER_NAME=SalesFlow AI

# ============================================================================
# WebSocket Configuration
# ============================================================================
# Port for WebSocket server (optional, defaults to 3002)
WEBSOCKET_PORT=3002
NEXT_PUBLIC_WEBSOCKET_PORT=3002

# ============================================================================
# Development Configuration
# ============================================================================
# Set to 'development' for local development, 'production' for production
NODE_ENV=development

# Enable debug logging (optional)
DEBUG=false

# ============================================================================
# Production Environment Variables (for deployment)
# ============================================================================
# For production deployment, also set:
# NEXTAUTH_URL=https://your-production-domain.com
# NODE_ENV=production
# DEBUG=false

# ============================================================================
# Setup Instructions
# ============================================================================
# 1. Copy this file: cp .env.sample .env.local
# 2. Generate NextAuth secret: openssl rand -base64 32
# 3. Set up Google OAuth in Google Cloud Console
# 4. Create Supabase project and get credentials
# 5. Get OpenAI API key from platform.openai.com
# 6. Update email whitelist with your email addresses
# 7. Set admin emails for dashboard access
# 8. Test the application: npm run dev

# ============================================================================
# Security Notes
# ============================================================================
# - Never commit .env.local to version control
# - Use different secrets for each environment (dev/staging/prod)
# - Rotate secrets regularly in production
# - Keep API keys secure and monitor usage
# - Review email whitelist regularly
# - Use strong, unique secrets for each environment
