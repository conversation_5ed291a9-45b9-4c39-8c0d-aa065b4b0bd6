# Docker Compose override for development
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
version: '3.8'

services:
  app:
    build:
      target: builder # Use builder stage for development
    environment:
      - NODE_ENV=development
      - NEXTAUTH_URL=http://localhost:3000
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules
      - /app/.next
    command: npm run dev
    ports:
      - "3000:3000"
    
  # Additional development services
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - salesflow-network
