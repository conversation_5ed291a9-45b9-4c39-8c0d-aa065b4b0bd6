// TypeScript type definitions for the SalesFlow AI Builder
// This will include types for assistants, products, users, etc.

import { DefaultSession } from 'next-auth';

export interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
}

export interface Assistant {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  prompt_template?: string;
  llm_model: string;
  whatsapp_connected: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  assistant_id: string;
  name: string;
  description: string;
  price?: number;
  category?: string;
  embedding?: number[];
  created_at: string;
  updated_at: string;
}

// Extend NextAuth types
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string;
    } & DefaultSession['user'];
    accessToken?: string;
  }

  interface User {
    id: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    accessToken?: string;
  }
}

export {};
