'use client';

import { useState } from 'react';
import { SAMPLE_AGENTS, type SampleAgent } from '@/lib/sampleData';

interface QuickStartProps {
  onSelectSample: (sampleData: SampleAgent['data']) => void;
  onCustomStart: () => void;
  onCustomizeTemplate?: (sampleData: SampleAgent['data']) => void;
  onBack?: () => void;
}

export default function QuickStart({ onSelectSample, onCustomStart, onCustomizeTemplate, onBack }: QuickStartProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [previewAgent, setPreviewAgent] = useState<SampleAgent | null>(null);

  const handleSelectAgent = (agent: SampleAgent) => {
    setSelectedAgent(agent.id);
    setTimeout(() => {
      onSelectSample(agent.data);
    }, 500); // Small delay for visual feedback
  };

  const handlePreviewAgent = (agent: SampleAgent) => {
    setPreviewAgent(agent);
  };

  const closePreview = () => {
    setPreviewAgent(null);
  };

  return (
    <div className="space-y-8">
      {/* Back Button */}
      {onBack && (
        <div className="flex justify-start">
          <button
            onClick={onBack}
            aria-label="Back to Assistants"
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            ← Back to Assistants
          </button>
        </div>
      )}

      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          🚀 Quick Start Your Sales Assistant
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Choose how you&apos;d like to get started. Use a sample assistant for quick testing,
          or create your own from scratch.
        </p>
      </div>

      {/* Quick Start Options */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Sample Agents */}
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              📋 Start with Sample Data
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Perfect for testing and learning. Choose from pre-configured assistants.
            </p>
          </div>

          <div className="space-y-4">
            {SAMPLE_AGENTS.map((agent) => (
              <div
                key={agent.id}
                role="button"
                tabIndex={0}
                aria-label={`Select ${agent.name} template`}
                className={`p-6 border rounded-lg cursor-pointer transition-all duration-200 ${
                  selectedAgent === agent.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 transform scale-105'
                    : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 hover:shadow-md'
                }`}
                onClick={() => handleSelectAgent(agent)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleSelectAgent(agent);
                  }
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {agent.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {agent.description}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                        {agent.industry}
                      </span>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs rounded-full">
                        {agent.data.productKnowledge.products.length} Products
                      </span>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      <strong>Use Case:</strong> {agent.useCase}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <strong>Company:</strong> {agent.data.businessSetup.companyName}
                    </div>

                    <div className="flex gap-2 mt-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePreviewAgent(agent);
                        }}
                        className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        👁️ Preview
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSelectAgent(agent);
                        }}
                        className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        🚀 Use Template
                      </button>
                    </div>
                  </div>

                  {selectedAgent === agent.id && (
                    <div className="ml-4 text-blue-600 dark:text-blue-400">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    </div>
                  )}
                </div>

                {/* Preview Products */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sample Products:
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {agent.data.productKnowledge.products.slice(0, 3).map((product, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                      >
                        {product.name}
                        {product.price && ` ($${product.price})`}
                      </span>
                    ))}
                    {agent.data.productKnowledge.products.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-xs rounded">
                        +{agent.data.productKnowledge.products.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              💡 Sample data can be customized after selection
            </p>
          </div>
        </div>

        {/* Custom Start */}
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              ✨ Start from Scratch
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Create a completely custom assistant tailored to your business.
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-8 text-center">
            <div className="text-6xl mb-4">🎨</div>
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Custom Configuration
            </h4>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Build your assistant step-by-step with complete control over every aspect
              of its behavior, personality, and knowledge base.
            </p>

            <div className="space-y-3 mb-6">
              <div className="flex items-center justify-center text-sm text-gray-600 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Complete customization freedom
              </div>
              <div className="flex items-center justify-center text-sm text-gray-600 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Industry-specific templates
              </div>
              <div className="flex items-center justify-center text-sm text-gray-600 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Advanced configuration options
              </div>
              <div className="flex items-center justify-center text-sm text-gray-600 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Perfect for production use
              </div>
            </div>

            <button
              onClick={onCustomStart}
              className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Start Custom Setup
            </button>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start">
              <div className="text-yellow-600 dark:text-yellow-400 mr-3 mt-0.5">
                💡
              </div>
              <div>
                <h5 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Recommendation
                </h5>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  New users should start with sample data to understand the platform,
                  then create custom assistants for production use.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
          Why Use Quick Start?
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl mb-2">⚡</div>
            <h5 className="font-medium text-gray-900 dark:text-white mb-1">Fast Setup</h5>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Get started in under 2 minutes with pre-configured data
            </p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">🧪</div>
            <h5 className="font-medium text-gray-900 dark:text-white mb-1">Perfect for Testing</h5>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Explore features and capabilities with realistic examples
            </p>
          </div>
          <div className="text-center">
            <div className="text-2xl mb-2">📚</div>
            <h5 className="font-medium text-gray-900 dark:text-white mb-1">Learn Best Practices</h5>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              See how successful businesses configure their assistants
            </p>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {previewAgent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {previewAgent.name} - Preview
                </h3>
                <button
                  onClick={closePreview}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Business Setup</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded text-sm">
                    <p><strong>Company:</strong> {previewAgent.data.businessSetup.companyName}</p>
                    <p><strong>Industry:</strong> {previewAgent.data.businessSetup.industry}</p>
                    <p className="mt-2">{previewAgent.data.businessSetup.description}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Assistant Configuration</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded text-sm">
                    <p><strong>Name:</strong> {previewAgent.data.salesAssistant.name}</p>
                    <p><strong>Personality:</strong> {previewAgent.data.salesAssistant.personality}</p>
                    <p><strong>Tone:</strong> {previewAgent.data.salesAssistant.tone}</p>
                    <div className="mt-2">
                      <strong>Objectives:</strong>
                      <ul className="list-disc list-inside mt-1">
                        {previewAgent.data.salesAssistant.objectives.map((obj, index) => (
                          <li key={index}>{obj}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Products & Services</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded text-sm">
                    <div className="grid gap-2">
                      {previewAgent.data.productKnowledge.products.map((product, index) => (
                        <div key={index} className="border-l-2 border-blue-500 pl-3">
                          <p><strong>{product.name}</strong> {product.price && `- $${product.price}`}</p>
                          <p className="text-gray-600 dark:text-gray-300">{product.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => {
                    closePreview();
                    handleSelectAgent(previewAgent);
                  }}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  🚀 Use This Template
                </button>
                {onCustomizeTemplate && (
                  <button
                    onClick={() => {
                      closePreview();
                      onCustomizeTemplate(previewAgent.data);
                    }}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                  >
                    ✏️ Customize Template
                  </button>
                )}
                <button
                  onClick={closePreview}
                  aria-label="Close preview modal"
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
