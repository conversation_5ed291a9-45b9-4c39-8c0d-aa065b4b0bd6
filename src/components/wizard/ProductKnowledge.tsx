'use client';

import { useState } from 'react';
import SaveButtons from './SaveButtons';

interface Product {
  name: string;
  description: string;
  price?: number;
  category?: string;
}

interface ProductKnowledgeData {
  products: Product[];
  knowledgeBase?: string;
}

interface ProductKnowledgeProps {
  data: ProductKnowledgeData;
  onComplete: (data: ProductKnowledgeData) => void;
  onBack: () => void;
  onSave?: (status?: 'draft' | 'configured') => Promise<any>;
  saving?: boolean;
  saveError?: string | null;
}

const SAMPLE_PRODUCTS = [
  {
    name: 'Premium Package',
    description: 'Our most comprehensive solution with full features and priority support',
    price: 299,
    category: 'Service',
  },
  {
    name: 'Starter Package',
    description: 'Perfect for small businesses getting started with essential features',
    price: 99,
    category: 'Service',
  },
];

export default function ProductKnowledge({ data, onComplete, onBack, onSave, saving, saveError }: ProductKnowledgeProps) {
  const [formData, setFormData] = useState<ProductKnowledgeData>(data);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [newProduct, setNewProduct] = useState<Product>({
    name: '',
    description: '',
    price: undefined,
    category: '',
  });

  const handleKnowledgeBaseChange = (value: string) => {
    setFormData(prev => ({ ...prev, knowledgeBase: value }));
  };

  const handleAddProduct = () => {
    if (!newProduct.name.trim() || !newProduct.description.trim()) {
      return;
    }

    setFormData(prev => ({
      ...prev,
      products: [...prev.products, { ...newProduct }],
    }));

    setNewProduct({
      name: '',
      description: '',
      price: undefined,
      category: '',
    });
    setShowAddProduct(false);
  };

  const handleRemoveProduct = (index: number) => {
    setFormData(prev => ({
      ...prev,
      products: prev.products.filter((_, i) => i !== index),
    }));
  };

  const handleUseSamples = () => {
    setFormData(prev => ({
      ...prev,
      products: [...prev.products, ...SAMPLE_PRODUCTS],
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.products.length === 0 && !formData.knowledgeBase?.trim()) {
      newErrors.general = 'Please add at least one product or provide a knowledge base description';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onComplete(formData);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          📦 Add your products & services
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Help your assistant understand what you sell so it can provide accurate information to customers.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Quick Start Options */}
        {formData.products.length === 0 && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
              Quick Start Options
            </h3>
            <div className="flex flex-wrap gap-3">
              <button
                type="button"
                onClick={handleUseSamples}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                Use Sample Products
              </button>
              <button
                type="button"
                onClick={() => setShowAddProduct(true)}
                className="px-4 py-2 border border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 text-sm rounded-lg transition-colors"
              >
                Add Custom Product
              </button>
            </div>
          </div>
        )}

        {/* Existing Products */}
        {formData.products.length > 0 && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Your Products ({formData.products.length})
              </h3>
              <button
                type="button"
                onClick={() => setShowAddProduct(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                + Add Product
              </button>
            </div>

            <div className="space-y-3">
              {formData.products.map((product, index) => (
                <div
                  key={index}
                  className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {product.name}
                        {product.price && (
                          <span className="ml-2 text-green-600 dark:text-green-400">
                            ${product.price}
                          </span>
                        )}
                        {product.category && (
                          <span className="ml-2 text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded">
                            {product.category}
                          </span>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {product.description}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveProduct(index)}
                      className="ml-4 text-red-600 hover:text-red-700 dark:text-red-400 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add Product Form */}
        {showAddProduct && (
          <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Add New Product
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Product Name *
                </label>
                <input
                  type="text"
                  value={newProduct.name}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
                  placeholder="Enter product name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Price (Optional)
                </label>
                <input
                  type="number"
                  value={newProduct.price || ''}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, price: e.target.value ? Number(e.target.value) : undefined }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category (Optional)
                </label>
                <input
                  type="text"
                  value={newProduct.category || ''}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
                  placeholder="e.g., Service, Product, Package"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description *
                </label>
                <textarea
                  rows={3}
                  value={newProduct.description}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
                  placeholder="Describe the product, its features, benefits, and who it's for..."
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-4">
              <button
                type="button"
                onClick={() => setShowAddProduct(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddProduct}
                disabled={!newProduct.name.trim() || !newProduct.description.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                Add Product
              </button>
            </div>
          </div>
        )}

        {/* Knowledge Base */}
        <div>
          <label htmlFor="knowledgeBase" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Additional Business Information (Optional)
          </label>
          <textarea
            id="knowledgeBase"
            rows={4}
            value={formData.knowledgeBase || ''}
            onChange={(e) => handleKnowledgeBaseChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Add any additional information about your business, policies, procedures, or frequently asked questions..."
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Include information like business hours, shipping policies, return policies, or common customer questions.
          </p>
        </div>

        {/* Error Message */}
        {errors.general && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
          </div>
        )}

        {/* Save Error */}
        {saveError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {saveError}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onBack}
              className="px-6 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              ← Back
            </button>

            {/* Save Buttons */}
            <SaveButtons
              onSave={onSave}
              onComplete={onComplete}
              formData={formData}
              saving={saving}
            />
          </div>

          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            Continue →
          </button>
        </div>
      </form>
    </div>
  );
}
