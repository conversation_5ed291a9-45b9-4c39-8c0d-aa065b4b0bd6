'use client';

import { useState } from 'react';
import SaveButtons from './SaveButtons';

interface BusinessSetupData {
  companyName: string;
  industry: string;
  description: string;
  website?: string;
  assistantName?: string;
  assistantDescription?: string;
}

interface BusinessSetupProps {
  data: BusinessSetupData;
  onComplete: (data: BusinessSetupData) => void;
  onBack: () => void;
  onSave?: (status?: 'draft' | 'configured') => Promise<any>;
  saving?: boolean;
  saveError?: string | null;
}

const INDUSTRIES = [
  'E-commerce',
  'Retail',
  'Technology',
  'Healthcare',
  'Education',
  'Real Estate',
  'Automotive',
  'Food & Beverage',
  'Fashion',
  'Beauty & Cosmetics',
  'Home & Garden',
  'Sports & Fitness',
  'Travel & Tourism',
  'Financial Services',
  'Consulting',
  'Other',
];

export default function BusinessSetup({ data, onComplete, onBack, onSave, saving, saveError }: BusinessSetupProps) {
  const [formData, setFormData] = useState<BusinessSetupData>(data);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: keyof BusinessSetupData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.assistantName?.trim()) {
      newErrors.assistantName = 'Assistant name is required';
    }

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    if (!formData.industry) {
      newErrors.industry = 'Please select an industry';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Business description is required';
    } else if (formData.description.trim().length < 20) {
      newErrors.description = 'Please provide a more detailed description (at least 20 characters)';
    }

    if (formData.website && formData.website.trim() && !isValidUrl(formData.website)) {
      newErrors.website = 'Please enter a valid website URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Validates URL format with enhanced logic
   * Assumptions:
   * - Accepts URLs with or without protocol (http/https)
   * - Automatically prepends https:// if no protocol is provided
   * - Validates basic URL structure using native URL constructor
   * - Allows common TLDs and subdomains
   *
   * Limitations:
   * - Does not verify if the URL is actually reachable
   * - Does not validate specific business requirements (e.g., no localhost)
   */
  const isValidUrl = (url: string): boolean => {
    try {
      const trimmedUrl = url.trim();

      // Basic format checks
      if (trimmedUrl.length === 0) return false;
      if (trimmedUrl.includes(' ')) return false; // URLs shouldn't contain spaces

      // Ensure protocol exists for URL constructor
      const urlWithProtocol = trimmedUrl.startsWith('http')
        ? trimmedUrl
        : `https://${trimmedUrl}`;

      const parsedUrl = new URL(urlWithProtocol);

      // Additional validation: ensure hostname exists and has valid format
      return parsedUrl.hostname.length > 0 &&
             parsedUrl.hostname.includes('.') &&
             !parsedUrl.hostname.startsWith('.') &&
             !parsedUrl.hostname.endsWith('.');
    } catch {
      return false;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onComplete(formData);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🏢 Tell us about your business
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Help us understand your business so we can create the perfect sales assistant for you.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Assistant Name */}
        <div>
          <label htmlFor="assistantName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Assistant Name *
          </label>
          <input
            type="text"
            id="assistantName"
            value={formData.assistantName || ''}
            onChange={(e) => handleInputChange('assistantName', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.assistantName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="e.g., Sarah, Alex, or your company assistant"
          />
          {errors.assistantName && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.assistantName}</p>
          )}
        </div>

        {/* Assistant Description */}
        <div>
          <label htmlFor="assistantDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Assistant Description (Optional)
          </label>
          <textarea
            id="assistantDescription"
            rows={2}
            value={formData.assistantDescription || ''}
            onChange={(e) => handleInputChange('assistantDescription', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Brief description of your assistant's purpose..."
          />
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This will be displayed on your assistant card.
          </p>
        </div>

        {/* Company Name */}
        <div>
          <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Company Name *
          </label>
          <input
            type="text"
            id="companyName"
            value={formData.companyName}
            onChange={(e) => handleInputChange('companyName', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.companyName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your company name"
          />
          {errors.companyName && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.companyName}</p>
          )}
        </div>

        {/* Industry */}
        <div>
          <label htmlFor="industry" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Industry *
          </label>
          <select
            id="industry"
            value={formData.industry}
            onChange={(e) => handleInputChange('industry', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.industry ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select your industry</option>
            {INDUSTRIES.map((industry) => (
              <option key={industry} value={industry}>
                {industry}
              </option>
            ))}
          </select>
          {errors.industry && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.industry}</p>
          )}
        </div>

        {/* Business Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Business Description *
          </label>
          <textarea
            id="description"
            rows={4}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Describe what your business does, your target customers, and what makes you unique..."
          />
          <div className="mt-1 flex justify-between">
            {errors.description ? (
              <p className="text-sm text-red-600 dark:text-red-400">{errors.description}</p>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This helps us create better sales conversations for your customers.
              </p>
            )}
            <p className="text-sm text-gray-400">
              {formData.description.length}/500
            </p>
          </div>
        </div>

        {/* Website (Optional) */}
        <div>
          <label htmlFor="website" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Website (Optional)
          </label>
          <input
            type="text"
            id="website"
            value={formData.website || ''}
            onChange={(e) => handleInputChange('website', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.website ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="https://yourwebsite.com"
          />
          {errors.website && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.website}</p>
          )}
        </div>

        {/* Save Error */}
        {saveError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {saveError}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onBack}
              className="px-6 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              ← Back
            </button>

            {/* Save Buttons */}
            <SaveButtons
              onSave={onSave}
              onComplete={onComplete}
              formData={formData}
              saving={saving}
            />
          </div>

          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            Continue →
          </button>
        </div>
      </form>
    </div>
  );
}
