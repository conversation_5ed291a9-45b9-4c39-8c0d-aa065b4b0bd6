'use client';

import { useState } from 'react';
import SaveButtons from './SaveButtons';

interface SalesAssistantData {
  name: string;
  personality: string;
  tone: string;
  language: string;
  objectives: string[];
  customPrompt?: string;
}

interface SalesAssistantProps {
  data: SalesAssistantData;
  onComplete: (data: SalesAssistantData) => void;
  onBack: () => void;
  onSave?: (status?: 'draft' | 'configured') => Promise<any>;
  saving?: boolean;
  saveError?: string | null;
  assistantName?: string;
}

const PERSONALITIES = [
  { value: 'professional', label: 'Professional', description: 'Formal, knowledgeable, and trustworthy' },
  { value: 'friendly', label: 'Friendly', description: 'Warm, approachable, and conversational' },
  { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic, passionate, and motivating' },
  { value: 'consultative', label: 'Consultative', description: 'Advisory, helpful, and solution-focused' },
];

const TONES = [
  { value: 'formal', label: 'Formal', description: 'Professional business communication' },
  { value: 'friendly', label: 'Friendly', description: 'Casual but respectful' },
  { value: 'casual', label: 'Casual', description: 'Relaxed and informal' },
  { value: 'empathetic', label: 'Empathetic', description: 'Understanding and supportive' },
];

const LANGUAGES = [
  { value: 'english', label: 'English' },
  { value: 'spanish', label: 'Spanish' },
  { value: 'french', label: 'French' },
  { value: 'german', label: 'German' },
  { value: 'chinese', label: 'Chinese' },
  { value: 'japanese', label: 'Japanese' },
  { value: 'portuguese', label: 'Portuguese' },
  { value: 'italian', label: 'Italian' },
];

const COMMON_OBJECTIVES = [
  'Generate leads and qualify prospects',
  'Answer product questions and provide information',
  'Schedule appointments or demos',
  'Handle customer support inquiries',
  'Process orders and payments',
  'Provide pricing and quotes',
  'Collect customer feedback',
  'Upsell and cross-sell products',
];

export default function SalesAssistant({ data, onComplete, onBack, onSave, saving, saveError, assistantName }: SalesAssistantProps) {
  const [formData, setFormData] = useState<SalesAssistantData>(data);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleInputChange = (field: keyof SalesAssistantData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleObjectiveToggle = (objective: string) => {
    const currentObjectives = formData.objectives;
    const newObjectives = currentObjectives.includes(objective)
      ? currentObjectives.filter(obj => obj !== objective)
      : [...currentObjectives, objective];

    handleInputChange('objectives', newObjectives);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.personality) {
      newErrors.personality = 'Please select a personality';
    }

    if (!formData.tone) {
      newErrors.tone = 'Please select a communication tone';
    }

    if (!formData.language) {
      newErrors.language = 'Please select a language';
    }

    if (formData.objectives.length === 0) {
      newErrors.objectives = 'Please select at least one objective';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onComplete(formData);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🤖 Configure your AI Sales Assistant
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Define how your assistant will interact with customers and what it should accomplish.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Assistant Name - Read Only Display */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Assistant Name
          </label>
          <div className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300">
            {assistantName || formData.name || 'Not set - please go back to Business Setup'}
          </div>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Assistant name is set in the Business Setup step. This is how your assistant will introduce itself to customers.
          </p>
        </div>

        {/* Personality */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Personality *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {PERSONALITIES.map((personality) => (
              <div
                key={personality.value}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  formData.personality === personality.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
                onClick={() => handleInputChange('personality', personality.value)}
              >
                <div className="font-medium text-gray-900 dark:text-white">
                  {personality.label}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {personality.description}
                </div>
              </div>
            ))}
          </div>
          {errors.personality && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.personality}</p>
          )}
        </div>

        {/* Communication Tone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Communication Tone *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {TONES.map((tone) => (
              <div
                key={tone.value}
                className={`p-3 border rounded-lg cursor-pointer transition-colors text-center ${
                  formData.tone === tone.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
                onClick={() => handleInputChange('tone', tone.value)}
              >
                <div className="font-medium text-gray-900 dark:text-white text-sm">
                  {tone.label}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                  {tone.description}
                </div>
              </div>
            ))}
          </div>
          {errors.tone && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.tone}</p>
          )}
        </div>

        {/* Language */}
        <div>
          <label htmlFor="language" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Primary Language *
          </label>
          <select
            id="language"
            value={formData.language}
            onChange={(e) => handleInputChange('language', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.language ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select primary language</option>
            {LANGUAGES.map((language) => (
              <option key={language.value} value={language.value}>
                {language.label}
              </option>
            ))}
          </select>
          {errors.language && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.language}</p>
          )}
        </div>

        {/* Objectives */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Sales Objectives * (Select all that apply)
          </label>
          <div className="space-y-2">
            {COMMON_OBJECTIVES.map((objective) => (
              <label
                key={objective}
                className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <input
                  type="checkbox"
                  checked={formData.objectives.includes(objective)}
                  onChange={() => handleObjectiveToggle(objective)}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">{objective}</span>
              </label>
            ))}
          </div>
          {errors.objectives && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.objectives}</p>
          )}
        </div>

        {/* Advanced Settings */}
        <div className="border-t pt-6">
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400"
          >
            {showAdvanced ? '▼' : '▶'} Advanced Settings (Optional)
          </button>

          {showAdvanced && (
            <div className="mt-4">
              <label htmlFor="customPrompt" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Custom Instructions
              </label>
              <textarea
                id="customPrompt"
                rows={4}
                value={formData.customPrompt || ''}
                onChange={(e) => handleInputChange('customPrompt', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Add specific instructions for how your assistant should behave..."
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Optional: Add specific guidelines or instructions for your assistant.
              </p>
            </div>
          )}
        </div>

        {/* Save Error */}
        {saveError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {saveError}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onBack}
              className="px-6 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              ← Back
            </button>

            {/* Save Buttons */}
            <SaveButtons
              onSave={onSave}
              onComplete={onComplete}
              formData={formData}
              saving={saving}
            />
          </div>

          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            Continue →
          </button>
        </div>
      </form>
    </div>
  );
}
