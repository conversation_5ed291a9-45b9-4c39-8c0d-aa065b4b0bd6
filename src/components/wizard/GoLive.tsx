'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { sessionManager } from '@/lib/whatsapp/session-manager';

interface GoLiveData {
  whatsappConnected: boolean;
  testingCompleted: boolean;
  isLive: boolean;
}

interface WhatsAppSession {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'qr_required';
  qrCode?: string;
  phoneNumber?: string;
  createdAt: string;
  lastActivity: string;
}

interface WizardData {
  businessSetup: {
    companyName: string;
    industry: string;
    description: string;
    website?: string;
  };
  salesAssistant: {
    name: string;
    personality: string;
    tone: string;
    language: string;
    objectives: string[];
    customPrompt?: string;
  };
  productKnowledge: {
    products: Array<{
      name: string;
      description: string;
      price?: number;
      category?: string;
    }>;
    knowledgeBase?: string;
  };
  goLive: GoLiveData;
}

interface GoLiveProps {
  data: GoLiveData;
  wizardData: WizardData;
  onComplete: (data: GoLiveData) => void;
  onBack: () => void;
}

export default function GoLive({ data, wizardData, onComplete, onBack }: GoLiveProps) {
  const { data: session } = useSession();
  const [formData, setFormData] = useState<GoLiveData>(data);
  const [currentStep, setCurrentStep] = useState<'summary' | 'whatsapp' | 'testing' | 'live'>('summary');
  const [testMessages, setTestMessages] = useState<Array<{ type: 'user' | 'assistant'; message: string }>>([]);
  const [testInput, setTestInput] = useState('');
  const [whatsappSession, setWhatsappSession] = useState<WhatsAppSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const [sessionId] = useState(`wizard-session-${Date.now()}`);

  useEffect(() => {
    return () => {
      // Cleanup WebSocket on unmount
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const connectWebSocket = (sessionId: string, attempt = 1, maxRetries = 5) => {
    try {
      const wsUrl = sessionManager.getWebSocketUrl();
      console.log(`[GoLive] Attempting WebSocket connection to ${wsUrl} for sessionId: ${sessionId}, attempt ${attempt}`);
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('[GoLive] WebSocket connected');
        wsRef.current = ws;
        ws.send(JSON.stringify({
          type: 'subscribe',
          sessionId: sessionId
        }));
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('[GoLive] WebSocket message received:', message);

          switch (message.type) {
            case 'qr_code':
              setWhatsappSession(prev => prev ? { ...prev, qrCode: message.data.qrCode, status: 'qr_required' } : {
                id: message.data.sessionId || sessionId,
                qrCode: message.data.qrCode,
                status: 'qr_required',
                createdAt: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
              });
              break;
            case 'connected':
              setWhatsappSession(prev => prev ? {
                ...prev,
                status: 'connected',
                phoneNumber: message.data.phoneNumber,
                qrCode: undefined
              } : null);
              setFormData(prev => ({ ...prev, whatsappConnected: true }));
              setCurrentStep('testing');
              break;
            case 'disconnected':
              setWhatsappSession(prev => prev ? { ...prev, status: 'disconnected', qrCode: undefined } : null);
              setFormData(prev => ({ ...prev, whatsappConnected: false }));
              break;
          }
        } catch (error) {
          console.error('[GoLive] Error parsing WebSocket message:', error, event.data);
        }
      };

      ws.onclose = (event) => {
        console.log('[GoLive] WebSocket disconnected', event);
        if (attempt < maxRetries) {
          console.log(`[GoLive] WebSocket retrying in 1s (attempt ${attempt + 1}/${maxRetries})`);
          setTimeout(() => connectWebSocket(sessionId, attempt + 1, maxRetries), 1000);
        } else {
          console.error('[GoLive] WebSocket failed to connect after max retries');
        }
      };

      ws.onerror = (error) => {
        console.error('[GoLive] WebSocket error:', error);
        // Let onclose handle the retry
      };
    } catch (error) {
      console.error('[GoLive] Error connecting WebSocket:', error);
    }
  };

  const handleWhatsAppConnect = async () => {
    setLoading(true);
    setError(null);

    try {
      // Check if user is authenticated
      if (!session?.user?.id) {
        setError('Please sign in to connect WhatsApp');
        return;
      }

      // Connect WebSocket first (removed delay)
      connectWebSocket(sessionId);

      // Create WhatsApp session with user context
      const response = await fetch('/api/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          userId: session.user.id,
          browserName: wizardData.businessSetup.companyName // Use company name as browser name
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setWhatsappSession(data.session);
      } else {
        setError(data.error || 'Failed to connect to WhatsApp');
      }
    } catch (error) {
      console.error('Error connecting:', error);
      setError('Failed to connect to WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  const handleSendTestMessage = async () => {
    if (!testInput.trim()) return;

    const userMessage = testInput;
    setTestMessages(prev => [...prev, { type: 'user', message: userMessage }]);
    setTestInput('');

    // Add loading message
    setTestMessages(prev => [...prev, { type: 'assistant', message: '...' }]);

    try {
      // Call OpenAI API with wizard configuration
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          assistantConfig: wizardData.salesAssistant,
          businessInfo: wizardData.businessSetup,
          products: wizardData.productKnowledge.products,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Replace loading message with actual response
        setTestMessages(prev =>
          prev.slice(0, -1).concat([{ type: 'assistant', message: data.response }])
        );
      } else {
        // Handle API error
        setTestMessages(prev =>
          prev.slice(0, -1).concat([{
            type: 'assistant',
            message: `Sorry, I encountered an error: ${data.error ?? 'Unknown error'}`
          }])
        );
      }
    } catch (error) {
      console.error('Chat API Error:', error);
      // Replace loading message with error message
      setTestMessages(prev =>
        prev.slice(0, -1).concat([{
          type: 'assistant',
          message: 'Sorry, I\'m having trouble connecting right now. Please try again.'
        }])
      );
    }
  };

  const handleCompleteWizard = async () => {
    try {
      console.log('🚀 Launching assistant...');
      console.log('Session ID:', sessionId);
      console.log('WhatsApp Session:', whatsappSession);
      console.log('Wizard Data:', wizardData);

      // Check if user is authenticated
      if (!session?.user?.id) {
        setError('Please sign in to save your assistant.');
        return;
      }

      // Check if WhatsApp is connected
      if (!whatsappSession || whatsappSession.status !== 'connected') {
        setError('Please connect WhatsApp first before launching the assistant.');
        return;
      }

      // Step 1: Save assistant configuration
      console.log('💾 Saving assistant configuration...');
      const assistantResponse = await fetch('/api/assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `${wizardData.businessSetup.companyName} Assistant`,
          description: `AI sales assistant for ${wizardData.businessSetup.companyName}`,
          businessSetup: wizardData.businessSetup,
          salesAssistant: wizardData.salesAssistant,
          productKnowledge: wizardData.productKnowledge,
          status: 'configured',
        }),
      });

      const assistantData = await assistantResponse.json();
      console.log('Assistant configuration response:', assistantData);

      if (!assistantResponse.ok) {
        console.error('❌ Failed to save assistant configuration:', assistantData);
        setError(`Failed to save assistant: ${assistantData.error || 'Unknown error'}`);
        return;
      }

      const assistantConfigId = assistantData.configuration.id;

      // Step 2: Update WhatsApp session with assistant configuration ID
      console.log('🔗 Linking WhatsApp session to assistant...');
      const updateSessionResponse = await fetch('/api/whatsapp/session', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          assistantConfigId,
          userId: session.user.id,
        }),
      });

      if (!updateSessionResponse.ok) {
        console.warn('⚠️ Failed to update session in database, but continuing...');
      }

      // Step 3: Enable assistant on the WhatsApp session
      console.log('🔗 Enabling assistant for WhatsApp session...');
      const linkResponse = await fetch('/api/whatsapp/assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          assistantConfigId,
        }),
      });

      const linkData = await linkResponse.json();
      console.log('Assistant link response:', linkData);

      if (linkResponse.ok) {
        console.log('✅ Assistant enabled successfully');
        // Navigate to assistant connected page
        window.location.href = `/assistant-connected?sessionId=${sessionId}&assistantId=${assistantConfigId}`;
      } else {
        console.error('❌ Failed to link assistant:', linkData);
        setError(`Failed to enable assistant: ${linkData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error launching assistant:', error);
      setError('Failed to launch assistant. Please try again.');
    }
  };

  const renderSummary = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🚀 Ready to Go Live!
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Review your configuration and save your agent, then connect WhatsApp to go live.
        </p>
      </div>

      {/* Configuration Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Business Setup</h3>
          <div className="space-y-2 text-sm">
            <div><span className="text-gray-600 dark:text-gray-400">Company:</span> {wizardData.businessSetup.companyName}</div>
            <div><span className="text-gray-600 dark:text-gray-400">Industry:</span> {wizardData.businessSetup.industry}</div>
            {wizardData.businessSetup.website && (
              <div><span className="text-gray-600 dark:text-gray-400">Website:</span> {wizardData.businessSetup.website}</div>
            )}
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Sales Assistant</h3>
          <div className="space-y-2 text-sm">
            <div><span className="text-gray-600 dark:text-gray-400">Name:</span> {wizardData.salesAssistant.name}</div>
            <div><span className="text-gray-600 dark:text-gray-400">Personality:</span> {wizardData.salesAssistant.personality}</div>
            <div><span className="text-gray-600 dark:text-gray-400">Language:</span> {wizardData.salesAssistant.language}</div>
            <div><span className="text-gray-600 dark:text-gray-400">Objectives:</span> {wizardData.salesAssistant.objectives.length} selected</div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 md:col-span-2">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Product Knowledge</h3>
          <div className="text-sm">
            <div className="mb-2"><span className="text-gray-600 dark:text-gray-400">Products:</span> {wizardData.productKnowledge.products.length} configured</div>
            {wizardData.productKnowledge.products.length > 0 && (
              <div className="space-y-1">
                {wizardData.productKnowledge.products.map((product, index) => (
                  <div key={index} className="text-gray-700 dark:text-gray-300">
                    • {product.name} {product.price && `($${product.price})`}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="text-center">
        <button
          onClick={() => setCurrentStep('whatsapp')}
          className="px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
        >
          Save Agent & Connect WhatsApp
        </button>
      </div>
    </div>
  );

  const renderWhatsAppConnection = () => (
    <div className="space-y-6 text-center">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          📱 Connect WhatsApp
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Connect your WhatsApp business account to enable your AI agent.
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      <div className="flex justify-center">
        <div className="w-64 h-64 bg-gray-100 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
          {!whatsappSession ? (
            <div className="text-center">
              <div className="text-6xl mb-4">📱</div>
              <p className="text-gray-600 dark:text-gray-400 mb-4">Click to generate QR code</p>
              <button
                onClick={handleWhatsAppConnect}
                disabled={loading}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {loading ? 'Connecting...' : 'Connect WhatsApp'}
              </button>
            </div>
          ) : whatsappSession.status === 'qr_required' && whatsappSession.qrCode ? (
            <div className="text-center">
              <img
                src={whatsappSession.qrCode}
                alt="WhatsApp QR Code"
                className="w-full h-full object-contain"
              />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Scan with WhatsApp
              </p>
            </div>
          ) : whatsappSession.status === 'connected' ? (
            <div className="text-center">
              <div className="text-6xl mb-4">✅</div>
              <p className="text-green-600 dark:text-green-400 font-medium">Connected!</p>
              {whatsappSession.phoneNumber && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  {whatsappSession.phoneNumber}
                </p>
              )}
            </div>
          ) : (
            <div className="text-center">
              <div className="text-6xl mb-4">⏳</div>
              <p className="text-gray-600 dark:text-gray-400">
                {whatsappSession.status === 'connecting' ? 'Connecting...' : 'Initializing...'}
              </p>
            </div>
          )}
        </div>
      </div>

      {whatsappSession?.status === 'connected' && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <p className="text-green-800 dark:text-green-200">
            ✅ WhatsApp successfully connected! Ready to save your agent and go live.
          </p>
        </div>
      )}
    </div>
  );

  const renderTesting = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🧪 Test Your Assistant
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Try out some conversations to see how your assistant responds.
        </p>
      </div>

      {/* Chat Interface */}
      <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
        <div className="bg-gray-50 dark:bg-gray-700 p-3 border-b border-gray-300 dark:border-gray-600">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {wizardData.salesAssistant.name.charAt(0)}
            </div>
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">{wizardData.salesAssistant.name}</div>
              <div className="text-xs text-green-600 dark:text-green-400">Online</div>
            </div>
          </div>
        </div>

        <div className="h-64 overflow-y-auto p-4 space-y-3">
          {testMessages.length === 0 && (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              Start a conversation to test your assistant
            </div>
          )}
          {testMessages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs px-3 py-2 rounded-lg ${
                  msg.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white'
                }`}
              >
                {msg.message}
              </div>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-300 dark:border-gray-600 p-3">
          <div className="flex gap-2">
            <input
              type="text"
              value={testInput}
              onChange={(e) => setTestInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendTestMessage()}
              placeholder="Type a test message..."
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <button
              onClick={handleSendTestMessage}
              disabled={!testInput.trim()}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors"
            >
              Send
            </button>
          </div>
        </div>
      </div>

      {/* Test Suggestions */}
      <div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Try these test messages:</p>
        <div className="flex flex-wrap gap-2">
          {['Hello', 'What products do you have?', 'How much does it cost?', 'Tell me about your company'].map((suggestion) => (
            <button
              key={suggestion}
              onClick={() => setTestInput(suggestion)}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      </div>

      <div className="text-center space-y-4">
        {error && (
          <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        )}

        <button
          onClick={handleCompleteWizard}
          disabled={!whatsappSession || whatsappSession.status !== 'connected'}
          className="px-8 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors"
        >
          🚀 Save Agent & Go Live
        </button>

        {(!whatsappSession || whatsappSession.status !== 'connected') && (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please connect WhatsApp first to save and activate your agent
          </p>
        )}
      </div>
    </div>
  );

  const renderLive = () => (
    <div className="space-y-6 text-center">
      <div>
        <div className="text-6xl mb-4">🎉</div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Congratulations!
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Your AI sales assistant is now live and ready to help your customers on WhatsApp.
        </p>
      </div>

      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4">
          What happens next?
        </h3>
        <div className="text-left space-y-2 text-green-700 dark:text-green-300">
          <div>✅ Your assistant will respond to WhatsApp messages automatically</div>
          <div>✅ Customer conversations will be logged for your review</div>
          <div>✅ You can update your assistant configuration anytime</div>
          <div>✅ Monitor performance through the dashboard</div>
        </div>
      </div>

      <div className="flex justify-center gap-4">
        <button
          onClick={() => window.location.href = '/'}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Go to Dashboard
        </button>
        <button
          onClick={() => setCurrentStep('testing')}
          className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          Test More
        </button>
      </div>
    </div>
  );

  return (
    <div>
      {currentStep === 'summary' && renderSummary()}
      {currentStep === 'whatsapp' && renderWhatsAppConnection()}
      {currentStep === 'testing' && renderTesting()}
      {currentStep === 'live' && formData.isLive && renderLive()}

      {/* Back Button */}
      {currentStep === 'summary' && (
        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={onBack}
            className="px-6 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            ← Back
          </button>
          <div></div>
        </div>
      )}
    </div>
  );
}
