'use client';

import { useState, useEffect } from 'react';
import { SAMPLE_AGENTS, type SampleAgent } from '@/lib/sampleData';
import {
  getCustomTemplates,
  saveCustomTemplate,
  deleteCustomTemplate,
  isStorageAvailable,
  type CustomTemplate
} from '@/lib/templateStorage';

interface TemplateManagerProps {
  onSelectTemplate: (template: SampleAgent['data']) => void;
  onClose: () => void;
}

export default function TemplateManager({ onSelectTemplate, onClose }: TemplateManagerProps) {
  const [activeTab, setActiveTab] = useState<'built-in' | 'custom'>('built-in');
  const [customTemplates, setCustomTemplates] = useState<CustomTemplate[]>([]);
  const [storageAvailable, setStorageAvailable] = useState(true);

  // Load custom templates on component mount
  useEffect(() => {
    const available = isStorageAvailable();
    setStorageAvailable(available);

    if (available) {
      setCustomTemplates(getCustomTemplates());
    }
  }, []);

  const handleSaveAsTemplate = (templateData: SampleAgent['data'], name: string, description: string) => {
    try {
      const newTemplate = saveCustomTemplate(templateData, name, description);
      setCustomTemplates(prev => [...prev, newTemplate]);
    } catch (error) {
      console.error('Failed to save template:', error);
      // Could add toast notification here
    }
  };

  const handleDeleteTemplate = (templateId: string) => {
    try {
      deleteCustomTemplate(templateId);
      setCustomTemplates(prev => prev.filter(t => t.id !== templateId));
    } catch (error) {
      console.error('Failed to delete template:', error);
      // Could add toast notification here
    }
  };

  const handleSelectBuiltInTemplate = (agent: SampleAgent) => {
    onSelectTemplate(agent.data);
    onClose();
  };

  const handleSelectCustomTemplate = (template: CustomTemplate) => {
    onSelectTemplate(template.data);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-600">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              📋 Template Manager
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* Tabs */}
          <div className="flex mt-4 border-b border-gray-200 dark:border-gray-600">
            <button
              onClick={() => setActiveTab('built-in')}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === 'built-in'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              Built-in Templates ({SAMPLE_AGENTS.length})
            </button>
            <button
              onClick={() => setActiveTab('custom')}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === 'custom'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              Custom Templates ({customTemplates.length})
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'built-in' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                Pre-built templates with industry-specific configurations and sample data.
              </div>

              {SAMPLE_AGENTS.map((agent) => (
                <div
                  key={agent.id}
                  className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-400 transition-colors"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {agent.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        {agent.description}
                      </p>
                      <div className="flex gap-2 mb-3">
                        <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded">
                          {agent.industry}
                        </span>
                        <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs rounded">
                          {agent.data.productKnowledge.products.length} Products
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Company: {agent.data.businessSetup.companyName} •
                        Assistant: {agent.data.salesAssistant.name}
                      </div>
                    </div>
                    <button
                      onClick={() => handleSelectBuiltInTemplate(agent)}
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      Use Template
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'custom' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                Your saved custom templates. Create new templates by customizing existing ones.
              </div>

              {!storageAvailable ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Storage Not Available
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Custom templates require browser storage. Please enable localStorage or use built-in templates.
                  </p>
                  <button
                    onClick={() => setActiveTab('built-in')}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    Browse Built-in Templates
                  </button>
                </div>
              ) : customTemplates.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">📝</div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No Custom Templates Yet
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Start with a built-in template and customize it to create your own.
                  </p>
                  <button
                    onClick={() => setActiveTab('built-in')}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    Browse Built-in Templates
                  </button>
                </div>
              ) : (
                customTemplates.map((template) => (
                  <div
                    key={template.id}
                    className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-400 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          {template.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                          {template.description}
                        </p>
                        <div className="flex gap-2 mb-3">
                          <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs rounded">
                            Custom
                          </span>
                          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
                            v{template.version}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Created: {new Date(template.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleSelectCustomTemplate(template)}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          Use Template
                        </button>
                        <button
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        <div className="p-6 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600 dark:text-gray-300">
              💡 Templates include pre-configured prompts, sample data, and industry-specific settings.
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
