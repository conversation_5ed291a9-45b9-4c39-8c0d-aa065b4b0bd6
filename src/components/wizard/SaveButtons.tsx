import React from 'react';

interface SaveButtonsProps {
  onSave?: (status: 'draft' | 'configured') => void;
  onComplete: (data: any) => void;
  formData: any;
  saving?: boolean;
}

export default function SaveButtons({ onSave, onComplete, formData, saving }: SaveButtonsProps) {
  if (!onSave) return null;

  return (
    <>
      <button
        type="button"
        onClick={() => {
          // Update the wizard data with current form data before saving
          onComplete(formData);
          onSave('draft');
        }}
        disabled={saving}
        className="px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {saving ? 'Saving...' : 'Save Draft'}
      </button>

      <button
        type="button"
        onClick={() => {
          // Update the wizard data with current form data before saving
          onComplete(formData);
          onSave('configured');
        }}
        disabled={saving}
        className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {saving ? 'Saving...' : 'Save Configured'}
      </button>
    </>
  );
}
