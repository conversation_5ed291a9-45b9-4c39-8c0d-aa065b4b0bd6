import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware() {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Define which routes require authentication
        const { pathname } = req.nextUrl;

        // Public routes that don't require authentication
        const publicRoutes = ['/', '/login', '/unauthorized'];

        // Public API patterns (using regex for flexibility)
        const publicApiPatterns = [
          /^\/api\/auth\//, // NextAuth routes
          /^\/api\/health$/, // Health check endpoint for Docker
          /^\/api\/whatsapp\//, // All WhatsApp endpoints (temporary for development)
        ];

        // Check if route matches public patterns
        const isPublicPattern = publicApiPatterns.some(pattern =>
          pattern.test(pathname)
        );

        if (publicRoutes.includes(pathname) || isPublicPattern) {
          return true;
        }

        // Protected routes require a valid token
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
