import OpenAI from 'openai';

// OpenAI Configuration
export const OPENAI_CONFIG = {
  // Default to GPT-4o mini, can be overridden by environment variable
  model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
  maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '500'),
  temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
  apiKey: process.env.OPENAI_API_KEY,
} as const;

// Validate configuration
if (!OPENAI_CONFIG.apiKey) {
  throw new Error('OPENAI_API_KEY environment variable is required');
}

// Single OpenAI client instance
export const openai = new OpenAI({
  apiKey: OPENAI_CONFIG.apiKey,
});

// Available models for reference
export const AVAILABLE_MODELS = {
  'gpt-4o-mini': {
    name: 'GPT-4o Mini',
    description: 'Fast, cost-effective model for most use cases',
    costPer1kTokens: 0.00015, // Input tokens
    maxTokens: 128000,
  },
  'gpt-4o': {
    name: 'GPT-4o',
    description: 'Most capable model with multimodal capabilities',
    costPer1kTokens: 0.005, // Input tokens
    maxTokens: 128000,
  },
  'gpt-4-turbo': {
    name: 'GPT-4 Turbo',
    description: 'High-performance model with large context window',
    costPer1kTokens: 0.01, // Input tokens
    maxTokens: 128000,
  },
  'gpt-3.5-turbo': {
    name: 'GPT-3.5 Turbo',
    description: 'Legacy model, still fast and cost-effective',
    costPer1kTokens: 0.0005, // Input tokens
    maxTokens: 16385,
  },
} as const;

// Type for valid model names
export type OpenAIModel = keyof typeof AVAILABLE_MODELS;

// Types for system prompt building
export interface AssistantConfig {
  name?: string;
  personality?: string;
  tone?: string;
  objectives?: string[];
  customPrompt?: string;
}

export interface BusinessInfo {
  companyName?: string;
  industry?: string;
  description?: string;
}

export interface Product {
  name: string;
  description: string;
  price?: number;
  category?: string;
}

// Validate model is supported
export function validateModel(model: string): model is OpenAIModel {
  return model in AVAILABLE_MODELS;
}

// Get model info
export function getModelInfo(model: OpenAIModel) {
  return AVAILABLE_MODELS[model];
}

// Create chat completion with centralized config
export async function createChatCompletion(
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  options?: {
    model?: OpenAIModel;
    maxTokens?: number;
    temperature?: number;
  }
) {
  const model = options?.model || OPENAI_CONFIG.model;

  // Validate model
  if (!validateModel(model)) {
    throw new Error(
      `Unsupported model: ${model}. Available models: ${Object.keys(
        AVAILABLE_MODELS
      ).join(', ')}`
    );
  }

  return await openai.chat.completions.create({
    model,
    messages,
    max_tokens: options?.maxTokens || OPENAI_CONFIG.maxTokens,
    temperature: options?.temperature || OPENAI_CONFIG.temperature,
  });
}

// Helper function to build system prompt
export function buildSystemPrompt(
  assistantConfig: AssistantConfig,
  businessInfo: BusinessInfo,
  products: Product[]
): string {
  const assistantName = assistantConfig?.name || 'Assistant';
  const companyName = businessInfo?.companyName || 'the company';
  const industry = businessInfo?.industry || 'business';
  const description = businessInfo?.description || 'a business';
  const personality = assistantConfig?.personality || 'professional';
  const tone = assistantConfig?.tone || 'friendly';
  const objectives = assistantConfig?.objectives || [];

  let prompt = `You are ${assistantName}, a ${personality} sales assistant for ${companyName}, ${description}

PERSONALITY & TONE:
- Personality: ${personality}
- Communication tone: ${tone}
- Industry: ${industry}

SALES OBJECTIVES:
${objectives.map((obj: string) => `- ${obj}`).join('\n')}

COMPANY INFORMATION:
${description}`;

  // Add product information if available
  if (products && products.length > 0) {
    prompt += `\n\nPRODUCTS & SERVICES:`;
    products.forEach(product => {
      prompt += `\n- ${product.name}`;
      if (product.price) prompt += ` ($${product.price})`;
      if (product.category) prompt += ` [${product.category}]`;
      prompt += `: ${product.description}`;
    });
  }

  // Add custom instructions if available
  if (assistantConfig?.customPrompt) {
    prompt += `\n\nADDITIONAL INSTRUCTIONS:
${assistantConfig.customPrompt}`;
  }

  prompt += `\n\nIMPORTANT GUIDELINES:
- Always introduce yourself as ${assistantName} from ${companyName}
- Be helpful, ${tone}, and ${personality}
- Focus on the customer's needs and how our products/services can help
- Ask clarifying questions when needed
- Provide specific product information when relevant
- Keep responses concise but informative
- If you don't know something, be honest and offer to connect them with a human representative`;

  return prompt;
}

// Prompt template management
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: string[];
  category: 'sales' | 'support' | 'general' | 'custom';
  version: string;
  createdAt: string;
  updatedAt: string;
}

// Default prompt templates
export const DEFAULT_PROMPT_TEMPLATES: PromptTemplate[] = [
  {
    id: 'sales-assistant-v1',
    name: 'Sales Assistant',
    description: 'Professional sales assistant for product inquiries',
    template: `You are {{assistantName}}, a {{personality}} sales assistant for {{companyName}}.

COMPANY: {{companyDescription}}
INDUSTRY: {{industry}}
PERSONALITY: {{personality}}
TONE: {{tone}}

SALES OBJECTIVES:
{{#each objectives}}
- {{this}}
{{/each}}

{{#if products}}
PRODUCTS & SERVICES:
{{#each products}}
- {{name}}: {{description}}
{{/each}}
{{/if}}

{{#if customInstructions}}
ADDITIONAL INSTRUCTIONS:
{{customInstructions}}
{{/if}}

GUIDELINES:
- Always introduce yourself as {{assistantName}} from {{companyName}}
- Be {{tone}} and {{personality}}
- Focus on customer needs and how our products/services can help
- Ask clarifying questions when needed
- Provide specific product information when relevant
- Keep responses concise but informative
- If unsure, offer to connect with a human representative`,
    variables: [
      'assistantName',
      'companyName',
      'companyDescription',
      'industry',
      'personality',
      'tone',
      'objectives',
      'products',
      'customInstructions',
    ],
    category: 'sales',
    version: '1.0.0',
    createdAt: '2025-01-27T00:00:00.000Z',
    updatedAt: '2025-01-27T00:00:00.000Z',
  },
  {
    id: 'support-assistant-v1',
    name: 'Support Assistant',
    description: 'Customer support assistant for issue resolution',
    template: `You are {{assistantName}}, a helpful customer support assistant for {{companyName}}.

COMPANY: {{companyDescription}}
PERSONALITY: {{personality}}
TONE: {{tone}}

SUPPORT OBJECTIVES:
- Resolve customer issues quickly and effectively
- Provide clear, step-by-step guidance
- Escalate complex issues when necessary
- Maintain a positive customer experience

{{#if products}}
PRODUCTS & SERVICES:
{{#each products}}
- {{name}}: {{description}}
{{/each}}
{{/if}}

GUIDELINES:
- Be empathetic and understanding
- Ask clarifying questions to understand the issue
- Provide clear, actionable solutions
- If you cannot resolve the issue, escalate to human support
- Always maintain a {{tone}} and {{personality}} demeanor`,
    variables: [
      'assistantName',
      'companyName',
      'companyDescription',
      'personality',
      'tone',
      'products',
    ],
    category: 'support',
    version: '1.0.0',
    createdAt: '2025-01-27T00:00:00.000Z',
    updatedAt: '2025-01-27T00:00:00.000Z',
  },
  {
    id: 'general-assistant-v1',
    name: 'General Assistant',
    description: 'General purpose assistant for various inquiries',
    template: `You are {{assistantName}}, a {{personality}} assistant for {{companyName}}.

COMPANY: {{companyDescription}}
INDUSTRY: {{industry}}
TONE: {{tone}}

{{#if customInstructions}}
INSTRUCTIONS:
{{customInstructions}}
{{/if}}

GUIDELINES:
- Be helpful, {{tone}}, and {{personality}}
- Provide accurate information about {{companyName}}
- If you don't know something, be honest and offer alternatives
- Maintain professional standards while being approachable`,
    variables: [
      'assistantName',
      'companyName',
      'companyDescription',
      'industry',
      'tone',
      'personality',
      'customInstructions',
    ],
    category: 'general',
    version: '1.0.0',
    createdAt: '2025-01-27T00:00:00.000Z',
    updatedAt: '2025-01-27T00:00:00.000Z',
  },
];

// Template rendering function (simple Handlebars-like syntax)
export function renderTemplate(
  template: string,
  variables: Record<string, unknown>
): string {
  let rendered = template;

  // Replace simple variables {{variable}}
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    rendered = rendered.replace(regex, String(value ?? ''));
  });

  // Handle arrays with {{#each array}} syntax
  const eachRegex = /{{#each (\w+)}}([\s\S]*?){{\/each}}/g;
  rendered = rendered.replace(eachRegex, (_match, arrayName, content) => {
    const array = variables[arrayName];
    if (!Array.isArray(array)) return '';

    return array
      .map(item => {
        let itemContent = content;
        if (typeof item === 'object') {
          Object.entries(item).forEach(([prop, val]) => {
            const propRegex = new RegExp(`{{${prop}}}`, 'g');
            itemContent = itemContent.replace(propRegex, String(val ?? ''));
          });
        } else {
          itemContent = itemContent.replace(/{{this}}/g, String(item));
        }
        return itemContent;
      })
      .join('');
  });

  // Handle conditionals {{#if variable}} syntax
  const ifRegex = /{{#if (\w+)}}([\s\S]*?){{\/if}}/g;
  rendered = rendered.replace(ifRegex, (_match, varName, content) => {
    const value = variables[varName];
    return value && (Array.isArray(value) ? value.length > 0 : true)
      ? content
      : '';
  });

  // Clean up any remaining template syntax
  rendered = rendered.replace(/{{[^}]*}}/g, '');

  return rendered.trim();
}

// Get template by ID
export function getPromptTemplate(
  templateId: string
): PromptTemplate | undefined {
  return DEFAULT_PROMPT_TEMPLATES.find(t => t.id === templateId);
}

// Get templates by category
export function getPromptTemplatesByCategory(
  category: PromptTemplate['category']
): PromptTemplate[] {
  return DEFAULT_PROMPT_TEMPLATES.filter(t => t.category === category);
}

// Enhanced system prompt builder with template support
export function buildSystemPromptFromTemplate(
  templateId: string,
  variables: Record<string, unknown>
): string {
  const template = getPromptTemplate(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  return renderTemplate(template.template, variables);
}

// Token usage tracking interface
export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  model: string;
  timestamp: string;
  cost?: number;
}

// Calculate cost based on model and token usage
export function calculateTokenCost(
  model: OpenAIModel,
  usage: TokenUsage
): number {
  const modelInfo = getModelInfo(model);
  const inputCost = (usage.promptTokens / 1000) * modelInfo.costPer1kTokens;
  const outputCost =
    (usage.completionTokens / 1000) * modelInfo.costPer1kTokens * 2; // Output typically costs 2x
  return inputCost + outputCost;
}

// Enhanced chat completion with template support
export async function createChatCompletionWithTemplate(
  templateId: string,
  templateVariables: Record<string, unknown>,
  userMessage: string,
  options?: {
    model?: OpenAIModel;
    maxTokens?: number;
    temperature?: number;
    conversationHistory?: OpenAI.Chat.Completions.ChatCompletionMessageParam[];
  }
): Promise<
  OpenAI.Chat.Completions.ChatCompletion & { tokenUsage: TokenUsage }
> {
  const systemPrompt = buildSystemPromptFromTemplate(
    templateId,
    templateVariables
  );

  const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
    { role: 'system', content: systemPrompt },
    ...(options?.conversationHistory ?? []),
    { role: 'user', content: userMessage },
  ];

  const completion = await createChatCompletion(messages, options);

  const tokenUsage: TokenUsage = {
    promptTokens: completion.usage?.prompt_tokens ?? 0,
    completionTokens: completion.usage?.completion_tokens ?? 0,
    totalTokens: completion.usage?.total_tokens ?? 0,
    model: completion.model,
    timestamp: new Date().toISOString(),
  };

  if (validateModel(completion.model)) {
    tokenUsage.cost = calculateTokenCost(completion.model, tokenUsage);
  }

  return { ...completion, tokenUsage };
}

// Log configuration (without sensitive data)
export function logOpenAIConfig() {
  console.log('OpenAI Configuration:', {
    model: OPENAI_CONFIG.model,
    maxTokens: OPENAI_CONFIG.maxTokens,
    temperature: OPENAI_CONFIG.temperature,
    apiKeyConfigured: !!OPENAI_CONFIG.apiKey,
    modelInfo: getModelInfo(OPENAI_CONFIG.model as OpenAIModel),
    availableTemplates: DEFAULT_PROMPT_TEMPLATES.length,
  });
}
