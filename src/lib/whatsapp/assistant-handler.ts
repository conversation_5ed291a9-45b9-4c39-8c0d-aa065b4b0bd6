import { webSocketManager } from '../websocket/manager';
import { createChatCompletion, buildSystemPrompt } from '../openai';

export interface AssistantConfig {
  salesAssistant?: any;
  businessSetup?: any;
  productKnowledge?: {
    products?: any[];
  };
  // For backward compatibility
  name?: string;
  personality?: string;
  tone?: string;
  objectives?: string[];
}

export interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: string;
  type: 'incoming' | 'outgoing';
}

class WhatsAppAssistantHandler {
  private activeAssistants: Map<string, AssistantConfig> = new Map();
  private processingMessages: Set<string> = new Set(); // Prevent duplicate processing

  /**
   * Enable assistant for a WhatsApp session
   */
  public enableAssistant(
    sessionId: string,
    assistantConfig: AssistantConfig
  ): void {
    console.log(`[Assistant] Enabling assistant for session: ${sessionId}`);
    console.log(
      `[Assistant] Assistant config:`,
      this.sanitizeAssistantConfig(assistantConfig)
    );
    this.activeAssistants.set(sessionId, assistantConfig);
    console.log(
      `[Assistant] Total active assistants: ${this.activeAssistants.size}`
    );

    // Set up WebSocket listener for this session
    this.setupWebSocketListener(sessionId);
  }

  /**
   * Disable assistant for a WhatsApp session
   */
  public disableAssistant(sessionId: string): void {
    console.log(`Disabling assistant for session: ${sessionId}`);
    this.activeAssistants.delete(sessionId);
  }

  /**
   * Check if assistant is enabled for a session
   */
  public isAssistantEnabled(sessionId: string): boolean {
    return this.activeAssistants.has(sessionId);
  }

  /**
   * Get assistant config for a session
   */
  public getAssistantConfig(sessionId: string): AssistantConfig | null {
    return this.activeAssistants.get(sessionId) || null;
  }

  /**
   * Setup WebSocket listener for incoming messages
   */
  private setupWebSocketListener(sessionId: string): void {
    // Note: In a real implementation, you'd want to set up the WebSocket listener
    // For now, we'll rely on the manual message processing
    console.log(`WebSocket listener setup for session: ${sessionId}`);
  }

  /**
   * Process incoming message and generate assistant response
   */
  public async processIncomingMessage(
    sessionId: string,
    message: WhatsAppMessage
  ): Promise<void> {
    console.log(`[Assistant] Processing message for session: ${sessionId}`);
    console.log(
      `[Assistant] Active assistants:`,
      Array.from(this.activeAssistants.keys())
    );

    // Check if assistant is enabled for this session
    const assistantConfig = this.activeAssistants.get(sessionId);
    if (!assistantConfig) {
      console.log(
        `[Assistant] No assistant configured for session: ${sessionId}`
      );
      return;
    }

    console.log(`[Assistant] Assistant found for session: ${sessionId}`);

    // Skip if message is outgoing (from us)
    if (message.type === 'outgoing') {
      return;
    }

    // Skip media messages for now - improved detection
    const mediaTypes = ['image', 'video', 'audio', 'document'];
    if (
      message.message === 'Media message' ||
      mediaTypes.includes(message.type as any)
    ) {
      return;
    }

    // Prevent duplicate processing
    const messageKey = `${sessionId}-${message.id}-${message.timestamp}`;
    if (this.processingMessages.has(messageKey)) {
      console.log(`Message already being processed: ${messageKey}`);
      return;
    }

    this.processingMessages.add(messageKey);

    try {
      console.log(
        `Processing message from ${message.from}: ${message.message}`
      );

      // Get assistant response
      const assistantReply = await this.getAssistantResponse(
        message.message,
        assistantConfig
      );

      if (assistantReply) {
        // Send response back via WhatsApp
        await this.sendWhatsAppMessage(sessionId, message.from, assistantReply);
        console.log(`Assistant replied to ${message.from}: ${assistantReply}`);
      } else {
        console.log(`No response generated for message: ${message.message}`);
      }
    } catch (error) {
      console.error('Error processing message with assistant:', error);

      // Send error message to user
      try {
        await this.sendWhatsAppMessage(
          sessionId,
          message.from,
          'Sorry, I encountered an error processing your message. Please try again.'
        );
      } catch (sendError) {
        console.error('Error sending error message:', sendError);
      }
    } finally {
      // Clean up processing flag after a delay
      setTimeout(() => {
        this.processingMessages.delete(messageKey);
      }, 5000);
    }
  }

  /**
   * Get response from assistant API
   */
  private async getAssistantResponse(
    message: string,
    assistantConfig: AssistantConfig
  ): Promise<string | null> {
    try {
      console.log(`[Assistant] Generating response for message: "${message}"`);

      // Build system prompt based on wizard configuration
      const systemPrompt = buildSystemPrompt(
        assistantConfig.salesAssistant || assistantConfig,
        assistantConfig.businessSetup,
        assistantConfig.productKnowledge?.products || []
      );

      // Call OpenAI API directly
      const completion = await createChatCompletion([
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: message,
        },
      ]);

      const response = completion.choices[0]?.message?.content ?? null;
      console.log(`[Assistant] Generated response: "${response}"`);

      return response;
    } catch (error) {
      console.error('Error generating assistant response:', error);
      return null;
    }
  }

  /**
   * Send message via WhatsApp API
   */
  private async sendWhatsAppMessage(
    sessionId: string,
    to: string,
    message: string
  ): Promise<void> {
    try {
      console.log(
        `[Assistant] Sending WhatsApp message to ${to}: "${message}"`
      );
      const baseUrl =
        process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/whatsapp/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          to,
          message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send WhatsApp message');
      }

      console.log(`Message sent successfully to ${to}`);
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Get all active assistant sessions
   */
  public getActiveSessions(): string[] {
    return Array.from(this.activeAssistants.keys());
  }

  /**
   * Sanitize assistant config for logging (remove sensitive data)
   */
  private sanitizeAssistantConfig(config: AssistantConfig): any {
    return {
      salesAssistant: config.salesAssistant
        ? {
            name: config.salesAssistant.name,
            tone: config.salesAssistant.tone,
            language: config.salesAssistant.language,
            personality: config.salesAssistant.personality,
            // Exclude customPrompt and objectives for brevity
          }
        : undefined,
      businessSetup: config.businessSetup
        ? {
            companyName: config.businessSetup.companyName,
            industry: config.businessSetup.industry,
            // Exclude description and website for brevity
          }
        : undefined,
      productKnowledge: config.productKnowledge
        ? {
            productCount: config.productKnowledge.products?.length || 0,
            // Exclude actual products and knowledge base for security
          }
        : undefined,
    };
  }

  /**
   * Clean up inactive sessions
   */
  public cleanup(): void {
    // In a real implementation, you might want to clean up old sessions
    // based on last activity or other criteria
    console.log('Cleaning up assistant handler...');
  }
}

// Singleton instance
export const whatsappAssistantHandler = new WhatsAppAssistantHandler();
