/**
 * WhatsApp Session Manager for Frontend
 * Handles session restoration and persistence when browser reconnects
 */

import { getWebSocketClientUrl } from '../config/websocket';

export interface SessionRestoreResult {
  success: boolean;
  session?: {
    id: string;
    status: string;
    qrCode?: string;
    phoneNumber?: string;
    createdAt: string;
    lastActivity: string;
  };
  message?: string;
  error?: string;
}

export class WhatsAppSessionManager {
  private static instance: WhatsAppSessionManager | null = null;
  private restoredSessions: Set<string> = new Set();

  public static getInstance(): WhatsAppSessionManager {
    if (!WhatsAppSessionManager.instance) {
      WhatsAppSessionManager.instance = new WhatsAppSessionManager();
    }
    return WhatsAppSessionManager.instance;
  }

  /**
   * Check if a session exists on the server
   */
  public async checkSession(sessionId: string): Promise<SessionRestoreResult> {
    try {
      const response = await fetch(
        `/api/whatsapp/restore?sessionId=${sessionId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error checking session:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to check session',
      };
    }
  }

  /**
   * Restore a session on the server
   */
  public async restoreSession(
    sessionId: string,
    userId?: string
  ): Promise<SessionRestoreResult> {
    try {
      // Check if we already tried to restore this session
      if (this.restoredSessions.has(sessionId)) {
        console.log(
          `Session ${sessionId} already restored in this browser session`
        );
        return await this.checkSession(sessionId);
      }

      const response = await fetch('/api/whatsapp/restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          userId,
        }),
      });

      const result = await response.json();

      if (result.success) {
        this.restoredSessions.add(sessionId);
        console.log(`Session ${sessionId} restored successfully`);
      }

      return result;
    } catch (error) {
      console.error('Error restoring session:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to restore session',
      };
    }
  }

  /**
   * Auto-restore sessions from localStorage when app loads
   */
  public async autoRestoreSessions(
    userId?: string
  ): Promise<SessionRestoreResult[]> {
    const results: SessionRestoreResult[] = [];

    try {
      // Get stored session IDs from localStorage
      const storedSessions = this.getStoredSessions();

      console.log(`Found ${storedSessions.length} stored sessions to restore`);

      for (const sessionId of storedSessions) {
        try {
          const result = await this.restoreSession(sessionId, userId);
          results.push(result);

          if (result.success) {
            console.log(
              `✅ Restored session: ${sessionId} (${result.session?.status})`
            );
          } else {
            console.log(
              `❌ Failed to restore session: ${sessionId} - ${result.error}`
            );
          }
        } catch (error) {
          console.error(`Error restoring session ${sessionId}:`, error);
          results.push({
            success: false,
            error: `Failed to restore ${sessionId}`,
          });
        }
      }
    } catch (error) {
      console.error('Error in auto-restore:', error);
    }

    return results;
  }

  /**
   * Store session ID in localStorage for future restoration
   */
  public storeSession(sessionId: string): void {
    try {
      const stored = this.getStoredSessions();
      if (!stored.includes(sessionId)) {
        stored.push(sessionId);
        localStorage.setItem('whatsapp_sessions', JSON.stringify(stored));
        console.log(`Stored session ${sessionId} for future restoration`);
      }
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  /**
   * Remove session from localStorage
   */
  public removeStoredSession(sessionId: string): void {
    try {
      const stored = this.getStoredSessions();
      const filtered = stored.filter(id => id !== sessionId);
      localStorage.setItem('whatsapp_sessions', JSON.stringify(filtered));
      this.restoredSessions.delete(sessionId);
      console.log(`Removed session ${sessionId} from storage`);
    } catch (error) {
      console.error('Error removing stored session:', error);
    }
  }

  /**
   * Get stored session IDs from localStorage
   */
  private getStoredSessions(): string[] {
    try {
      const stored = localStorage.getItem('whatsapp_sessions');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting stored sessions:', error);
      return [];
    }
  }

  /**
   * Clear all stored sessions
   */
  public clearStoredSessions(): void {
    try {
      localStorage.removeItem('whatsapp_sessions');
      this.restoredSessions.clear();
      console.log('Cleared all stored sessions');
    } catch (error) {
      console.error('Error clearing stored sessions:', error);
    }
  }

  /**
   * Get WebSocket URL for client connections
   */
  public getWebSocketUrl(): string {
    return getWebSocketClientUrl();
  }
}

// Export singleton instance
export const sessionManager = WhatsAppSessionManager.getInstance();
