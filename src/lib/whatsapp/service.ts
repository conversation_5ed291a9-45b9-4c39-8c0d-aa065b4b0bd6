import makeWASocket, {
  DisconnectReason,
  useMultiFileAuthState,
  WASocket,
  ConnectionState,
} from '@whiskeysockets/baileys';
import { CustomWebSocket } from '../websocket/custom-ws';
import { Boom } from '@hapi/boom';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { WhatsAppSessionDatabase } from '../database/whatsapp-sessions';
import { AssistantConfigurationDatabase } from '../database/assistant-configurations';
import { whatsappAssistantHandler } from './assistant-handler';

// Utility function to extract phone number from WhatsApp user ID
function extractPhoneNumber(userId: string): string | undefined {
  try {
    console.log('Extracting phone number from:', userId);

    // WhatsApp user ID formats:
    // - <EMAIL>
    // - phoneNumber:<EMAIL>
    // - phoneNumber:suffix

    if (userId.includes('@')) {
      // Extract the part before @
      const beforeAt = userId.split('@')[0];
      if (beforeAt.includes(':')) {
        // Extract the part before :
        return beforeAt.split(':')[0];
      } else {
        return beforeAt;
      }
    } else if (userId.includes(':')) {
      return userId.split(':')[0];
    } else {
      return userId;
    }
  } catch (error) {
    console.error('Error extracting phone number:', error);
    return undefined;
  }
}

export interface WhatsAppSession {
  id: string;
  socket: WASocket | null;
  qrCode: string | null;
  status: 'disconnected' | 'connecting' | 'connected' | 'qr_required';
  phoneNumber?: string;
  userId?: string;
  assistantConfigId?: string;
  createdAt: Date;
  lastActivity: Date;
}

export interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: Date;
  type: 'incoming' | 'outgoing';
}

class WhatsAppService {
  private sessions: Map<string, WhatsAppSession> = new Map();
  private sessionDir: string;
  private eventCallbacks: Map<string, (event: string, data: any) => void> =
    new Map();

  constructor() {
    this.sessionDir =
      process.env.WHATSAPP_SESSION_PATH || './whatsapp-sessions';
    this.ensureSessionDirectory();

    // Restore existing sessions on startup
    this.restoreExistingSessions();

    // Set up periodic session monitoring
    this.setupSessionMonitoring();
  }

  private ensureSessionDirectory() {
    if (!fs.existsSync(this.sessionDir)) {
      fs.mkdirSync(this.sessionDir, { recursive: true });
    }
  }

  private getSessionPath(sessionId: string): string {
    return path.join(this.sessionDir, sessionId);
  }

  public async createSession(
    sessionId: string,
    userId?: string,
    assistantConfigId?: string,
    customBrowserName?: string
  ): Promise<WhatsAppSession> {
    try {
      // Check if session already exists and is connected
      const existingSession = this.sessions.get(sessionId);
      let phoneNumber: string | undefined = undefined;
      if (
        existingSession &&
        existingSession.status === 'connected' &&
        existingSession.phoneNumber
      ) {
        phoneNumber = existingSession.phoneNumber;
      }
      // Clean up existing session if it exists
      // await this.destroySession(sessionId); <--- This line is being commented out

      const sessionPath = this.getSessionPath(sessionId);

      // Ensure the specific session directory exists before useMultiFileAuthState
      if (!fs.existsSync(sessionPath)) {
        fs.mkdirSync(sessionPath, { recursive: true });
      }

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionPath);

      // Create a proper logger that satisfies Baileys requirements
      const createLogger = (): any => ({
        level: 'silent',
        child: () => createLogger(),
        trace: () => {},
        debug: () => {},
        info: () => {},
        warn: () => {},
        error: () => {},
        fatal: () => {},
      });

      // Create WhatsApp socket with proper logger and custom browser name
      const browserName =
        customBrowserName ||
        process.env.WHATSAPP_BROWSER_NAME ||
        'Sales Flow AI';

      const socket = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        logger: createLogger(),
        browser: [browserName, 'Chrome', '1.0.0'], // Custom device name that appears in WhatsApp
        // WebSocket configuration for Docker compatibility
        connectTimeoutMs: 60000, // Increase connection timeout
        defaultQueryTimeoutMs: 60000, // Increase query timeout
        // Force specific WebSocket implementation to fix Docker masking issues
        ws: CustomWebSocket,
        // Disable history sync to reduce WebSocket load
        shouldSyncHistoryMessage: () => false,
        shouldIgnoreJid: () => false,
        // Alternative options:
        // browser: ['Your Company Name', 'Desktop', '1.0.0'],
        // browser: ['AI Assistant', 'Web', '1.0.0'],
      });

      // Create session object
      const session: WhatsAppSession = {
        id: sessionId,
        socket,
        qrCode: null,
        status: 'connecting',
        userId,
        assistantConfigId,
        phoneNumber, // Set phone number if available
        createdAt: new Date(),
        lastActivity: new Date(),
      };

      // Persist session to database if userId is provided
      if (userId) {
        try {
          await WhatsAppSessionDatabase.createSession({
            sessionId,
            userId,
            assistantConfigId,
            phoneNumber, // Pass phone number if available
          });
          console.log('Session persisted to database');
        } catch (error) {
          console.error('Error persisting session to database:', error);
          // Continue without database persistence for now
        }
      }

      // Store session
      this.sessions.set(sessionId, session);

      // Set up event handlers
      this.setupEventHandlers(sessionId, socket, saveCreds);

      return session;
    } catch (error) {
      console.error('Error creating WhatsApp session:', error);
      throw new Error('Failed to create WhatsApp session');
    }
  }

  private setupEventHandlers(
    sessionId: string,
    socket: WASocket,
    saveCreds: () => Promise<void>
  ) {
    let isRestarting = false;

    socket.ev.on(
      'connection.update',
      async (update: Partial<ConnectionState>) => {
        console.log('connection.update event fired:', update);
        const session = this.sessions.get(sessionId);
        if (!session) return;

        const { connection, lastDisconnect, qr } = update;

        console.log('Connection update:', update, { sessionId });

        if (qr) {
          try {
            console.log('Generating QR code for session:', sessionId);
            // Generate QR code as data URL
            const qrCodeDataUrl = await QRCode.toDataURL(qr);
            session.qrCode = qrCodeDataUrl;
            session.status = 'qr_required';
            session.lastActivity = new Date();

            console.log('QR code generated successfully');
            // Notify listeners
            this.notifyEvent(sessionId, 'qr_code', { qrCode: qrCodeDataUrl });
          } catch (error) {
            console.error('Error generating QR code:', error);
          }
        }

        // Handle connection state changes
        if (connection === 'close') {
          const statusCode = (lastDisconnect?.error as Boom)?.output
            ?.statusCode;
          console.log('Connection closed with status:', statusCode);

          session.status = 'disconnected';
          session.qrCode = null;

          if (statusCode === DisconnectReason.loggedOut) {
            console.log('Device logged out');
            this.notifyEvent(sessionId, 'disconnected', {
              reason: 'logged_out',
            });
          } else if (statusCode === DisconnectReason.restartRequired) {
            console.log(
              'Restart required - waiting for credentials to be saved'
            );
            if (!isRestarting) {
              isRestarting = true;
              // Wait for credentials to be saved
              await new Promise(resolve => setTimeout(resolve, 2000));
              try {
                await this.createSession(sessionId);
                console.log('Session restarted successfully');
              } catch (error) {
                console.error('Error restarting session:', error);
                this.notifyEvent(sessionId, 'disconnected', {
                  reason: 'restart_failed',
                });
              }
              isRestarting = false;
            }
          } else {
            console.log('Connection lost, reason:', statusCode);
            this.notifyEvent(sessionId, 'disconnected', {
              reason: 'connection_lost',
            });
          }
        } else if (connection === 'open') {
          console.log('WhatsApp connected successfully');
          console.log(
            'Socket user data:',
            JSON.stringify(socket.user, null, 2)
          );

          session.status = 'connected';
          session.qrCode = null;
          session.lastActivity = new Date();

          // Extract phone number using utility function
          if (socket.user?.id) {
            session.phoneNumber = extractPhoneNumber(socket.user.id);
            console.log('Extracted phone number:', session.phoneNumber);
          } else {
            console.log('No user.id found on socket.user');
            session.phoneNumber = undefined;
          }

          // Update database with connection status and phone number
          // Always try to update database since session might exist from initial creation
          try {
            const updatePayload = {
              status: 'connected' as const,
              phone_number: session.phoneNumber,
              qr_code: undefined, // Clear QR code when connected
            };
            console.log('Updating database with payload:', updatePayload);

            const result = await WhatsAppSessionDatabase.updateSession(
              sessionId,
              updatePayload
            );

            if (result) {
              console.log(
                'Database updated successfully:',
                result.phone_number
              );
            } else {
              console.log(
                'Session not found in database, might be file-based session'
              );
            }
          } catch (error) {
            console.error('Error updating session in database:', error);
          }

          this.notifyEvent(sessionId, 'connected', {
            phoneNumber: session.phoneNumber,
          });
        } else if (connection === 'connecting') {
          console.log('WhatsApp connecting...');
          session.status = 'connecting';
          session.lastActivity = new Date();
          this.notifyEvent(sessionId, 'connecting', {});
        }
      }
    );

    // Handle credentials update
    socket.ev.on('creds.update', async () => {
      console.log('Credentials update received');
      try {
        await saveCreds();
        console.log('Credentials saved successfully');
      } catch (error) {
        console.error('Error saving credentials:', error);
      }
    });

    socket.ev.on('messages.upsert', async messageUpdate => {
      const session = this.sessions.get(sessionId);
      if (!session) return;

      session.lastActivity = new Date();

      for (const message of messageUpdate.messages) {
        if (message.key.fromMe) return; // Skip own messages

        const messageText =
          message.message?.conversation ||
          message.message?.extendedTextMessage?.text ||
          'Media message';

        const whatsappMessage: WhatsAppMessage = {
          id: message.key.id || '',
          from: message.key.remoteJid || '', // Keep original JID for replies
          to:
            extractPhoneNumber(socket.user?.id || '') || socket.user?.id || '',
          message: messageText,
          timestamp: new Date((message.messageTimestamp as number) * 1000),
          type: 'incoming',
        };

        this.notifyEvent(sessionId, 'message_received', whatsappMessage);

        // Store message in database
        if (session.userId) {
          try {
            await WhatsAppSessionDatabase.storeMessage({
              sessionId,
              messageId: whatsappMessage.id,
              fromNumber:
                extractPhoneNumber(whatsappMessage.from) ||
                whatsappMessage.from, // Extract for storage
              toNumber: whatsappMessage.to,
              messageText: whatsappMessage.message,
              direction: 'incoming',
              timestamp: whatsappMessage.timestamp,
            });
          } catch (error) {
            console.error('Error storing message in database:', error);
          }
        }

        // Automatically process message with assistant if session has assistant config
        await this.processMessageWithAssistant(sessionId, whatsappMessage);
      }
    });
  }

  public async sendMessage(
    sessionId: string,
    to: string,
    message: string
  ): Promise<WhatsAppMessage | null> {
    const session = this.sessions.get(sessionId);

    if (!session || !session.socket || session.status !== 'connected') {
      throw new Error('WhatsApp session not connected');
    }

    try {
      const result = await session.socket.sendMessage(to, { text: message });

      const whatsappMessage: WhatsAppMessage = {
        id: result?.key?.id || '',
        from:
          extractPhoneNumber(session.socket.user?.id || '') ||
          session.socket.user?.id ||
          '',
        to: extractPhoneNumber(to) || to,
        message,
        timestamp: new Date(),
        type: 'outgoing',
      };

      session.lastActivity = new Date();
      this.notifyEvent(sessionId, 'message_sent', whatsappMessage);

      // Store outgoing message in database
      if (session.userId) {
        try {
          await WhatsAppSessionDatabase.storeMessage({
            sessionId,
            messageId: whatsappMessage.id,
            fromNumber: whatsappMessage.from,
            toNumber: whatsappMessage.to,
            messageText: whatsappMessage.message,
            direction: 'outgoing',
            timestamp: whatsappMessage.timestamp,
          });
        } catch (error) {
          console.error('Error storing outgoing message in database:', error);
        }
      }

      return whatsappMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  public getSession(sessionId: string): WhatsAppSession | null {
    return this.sessions.get(sessionId) || null;
  }

  public async destroySession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);

    if (session?.socket) {
      try {
        // Only logout if the socket is connected
        if (session.status === 'connected') {
          await session.socket.logout();
        }
      } catch (error) {
        // Ignore logout errors as the connection might already be closed
        console.log(
          'Session cleanup - connection already closed:',
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      try {
        session.socket.end(undefined);
      } catch (error) {
        // Ignore end errors
        console.log(
          'Session cleanup - socket end error:',
          error instanceof Error ? error.message : 'Unknown error'
        );
      }
    }

    this.sessions.delete(sessionId);
    this.eventCallbacks.delete(sessionId);

    // Clean up session files
    const sessionPath = this.getSessionPath(sessionId);
    if (fs.existsSync(sessionPath)) {
      try {
        fs.rmSync(sessionPath, { recursive: true, force: true });
      } catch (error) {
        console.error('Error cleaning up session files:', error);
      }
    }
  }

  public onEvent(
    sessionId: string,
    callback: (event: string, data: any) => void
  ): void {
    this.eventCallbacks.set(sessionId, callback);
  }

  private notifyEvent(sessionId: string, event: string, data: any): void {
    const callback = this.eventCallbacks.get(sessionId);
    if (callback) {
      callback(event, data);
    }
  }

  public getAllSessions(): WhatsAppSession[] {
    return Array.from(this.sessions.values()).map(session => ({
      ...session,
      socket: null, // Don't expose socket in public API
    }));
  }

  public async cleanupInactiveSessions(
    maxAgeMinutes: number = 30
  ): Promise<void> {
    const now = new Date();
    const sessionsToCleanup: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      const ageMinutes =
        (now.getTime() - session.lastActivity.getTime()) / (1000 * 60);

      if (ageMinutes > maxAgeMinutes && session.status !== 'connected') {
        sessionsToCleanup.push(sessionId);
      }
    }

    for (const sessionId of sessionsToCleanup) {
      await this.destroySession(sessionId);
    }
  }

  /**
   * Restore existing WhatsApp sessions from database on server startup
   */
  private async restoreExistingSessions(): Promise<void> {
    try {
      console.log('[WhatsApp] Restoring existing sessions from database...');

      // Get all connected sessions from database
      const connectedSessions =
        await WhatsAppSessionDatabase.getConnectedSessions();

      for (const dbSession of connectedSessions) {
        try {
          console.log(`[WhatsApp] Restoring session: ${dbSession.session_id}`);

          // Check if session files exist
          const sessionPath = this.getSessionPath(dbSession.session_id);
          if (fs.existsSync(sessionPath)) {
            // Restore the session without creating a new one
            await this.restoreSession(
              dbSession.session_id,
              dbSession.user_id,
              dbSession.assistant_config_id || undefined
            );
          } else {
            console.log(
              `[WhatsApp] Session files not found for ${dbSession.session_id}, marking as disconnected`
            );
            // Mark as disconnected in database if files don't exist
            await WhatsAppSessionDatabase.updateSession(dbSession.session_id, {
              status: 'disconnected',
            });
          }
        } catch (error) {
          console.error(
            `[WhatsApp] Error restoring session ${dbSession.session_id}:`,
            error
          );
        }
      }

      console.log(`[WhatsApp] Restored ${connectedSessions.length} sessions`);
    } catch (error) {
      console.error('[WhatsApp] Error restoring existing sessions:', error);
    }
  }

  /**
   * Set up periodic monitoring of WhatsApp sessions
   */
  private setupSessionMonitoring(): void {
    // Monitor sessions every 5 minutes
    setInterval(async () => {
      try {
        await this.monitorSessions();
      } catch (error) {
        console.error('[WhatsApp] Error in session monitoring:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    console.log('[WhatsApp] Session monitoring started');
  }

  /**
   * Monitor active sessions and sync with database
   */
  private async monitorSessions(): Promise<void> {
    console.log('[WhatsApp] Monitoring sessions...');

    for (const [sessionId, session] of this.sessions) {
      try {
        // Update last activity in database
        await WhatsAppSessionDatabase.updateSession(sessionId, {
          status: session.status,
          phone_number: session.phoneNumber,
        });

        // Log session status
        console.log(
          `[WhatsApp] Session ${sessionId}: ${session.status} (${
            session.phoneNumber || 'no phone'
          })`
        );
      } catch (error) {
        console.error(
          `[WhatsApp] Error monitoring session ${sessionId}:`,
          error
        );
      }
    }
  }

  /**
   * Restore a WhatsApp session from existing credentials
   */
  private async restoreSession(
    sessionId: string,
    userId?: string,
    assistantConfigId?: string
  ): Promise<WhatsAppSession | null> {
    try {
      console.log(`[WhatsApp] Restoring session: ${sessionId}`);

      // Check if session already exists in memory
      if (this.sessions.has(sessionId)) {
        console.log(`[WhatsApp] Session ${sessionId} already exists in memory`);
        return this.sessions.get(sessionId)!;
      }

      const sessionPath = this.getSessionPath(sessionId);

      // Check if session files exist
      if (!fs.existsSync(sessionPath)) {
        console.log(`[WhatsApp] No session files found for ${sessionId}`);
        return null;
      }

      // Initialize auth state from existing files
      const { state, saveCreds } = await useMultiFileAuthState(sessionPath);

      // Create a proper logger
      const createLogger = (): any => ({
        level: 'silent',
        child: () => createLogger(),
        trace: () => {},
        debug: () => {},
        info: () => {},
        warn: () => {},
        error: () => {},
        fatal: () => {},
      });

      // Get browser name from environment or default
      const browserName = process.env.WHATSAPP_BROWSER_NAME || 'Sales Flow AI';

      // Create WhatsApp socket
      const socket = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        logger: createLogger(),
        browser: [browserName, 'Chrome', '1.0.0'],
        // WebSocket configuration for Docker compatibility
        connectTimeoutMs: 60000, // Increase connection timeout
        defaultQueryTimeoutMs: 60000, // Increase query timeout
        // Force specific WebSocket implementation to fix Docker masking issues
        ws: CustomWebSocket,
        // Disable history sync to reduce WebSocket load
        shouldSyncHistoryMessage: () => false,
        shouldIgnoreJid: () => false,
      });

      // Create session object
      const session: WhatsAppSession = {
        id: sessionId,
        socket,
        qrCode: null,
        status: 'connecting',
        userId,
        assistantConfigId,
        phoneNumber: undefined, // Will be set when connected
        createdAt: new Date(),
        lastActivity: new Date(),
      };

      // Store session
      this.sessions.set(sessionId, session);

      // Set up event handlers
      this.setupEventHandlers(sessionId, socket, saveCreds);

      console.log(`[WhatsApp] Session ${sessionId} restored successfully`);
      return session;
    } catch (error) {
      console.error(`[WhatsApp] Error restoring session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Process incoming message with assistant automatically
   */
  private async processMessageWithAssistant(
    sessionId: string,
    message: WhatsAppMessage
  ): Promise<void> {
    try {
      console.log(
        `[WhatsApp] Auto-processing message for session: ${sessionId}`
      );

      // Get session from database to check for assistant configuration
      const session = await WhatsAppSessionDatabase.getSession(sessionId);
      if (!session || !session.assistant_config_id) {
        console.log(`[WhatsApp] No assistant config for session: ${sessionId}`);
        return;
      }

      // Get assistant configuration from database
      const assistantConfig =
        await AssistantConfigurationDatabase.getConfiguration(
          session.assistant_config_id
        );

      if (!assistantConfig) {
        console.log(
          `[WhatsApp] Assistant config not found: ${session.assistant_config_id}`
        );
        return;
      }

      console.log(
        `[WhatsApp] Found assistant config for session: ${sessionId}`
      );

      // Enable assistant with the configuration from database
      whatsappAssistantHandler.enableAssistant(sessionId, {
        salesAssistant: assistantConfig.sales_assistant,
        businessSetup: assistantConfig.business_setup,
        productKnowledge: assistantConfig.product_knowledge,
      });

      console.log(`[WhatsApp] Processing message with assistant...`);
      // Process the message with assistant (convert timestamp to string)
      const assistantMessage = {
        ...message,
        timestamp: message.timestamp.toISOString(),
      };
      await whatsappAssistantHandler.processIncomingMessage(
        sessionId,
        assistantMessage
      );

      console.log(`[WhatsApp] Message processed successfully with assistant`);
    } catch (error) {
      console.error(
        `[WhatsApp] Error processing message with assistant:`,
        error
      );
    }
  }
}

// Singleton instance
export const whatsappService = new WhatsAppService();
