/**
 * Custom WebSocket implementation to fix Docker masking issues
 * This bypasses the bundling issues that cause the unmask function error
 */

import { WebSocket as WSClass } from 'ws';

// Create a custom WebSocket class that handles masking properly
export class CustomWebSocket extends WSClass {
  constructor(address: string | URL, protocols?: string | string[], options?: any) {
    // Force specific options to prevent masking issues
    const customOptions = {
      ...options,
      perMessageDeflate: false,
      compression: 'DISABLED',
      maxPayload: 100 * 1024 * 1024, // 100MB
      // Disable masking for server-side connections
      mask: false,
    };

    super(address, protocols, customOptions);
  }
}

// Export a factory function that creates the custom WebSocket
export function createCustomWebSocket(address: string | URL, protocols?: string | string[], options?: any) {
  return new CustomWebSocket(address, protocols, options);
}

// Default export for compatibility
export default CustomWebSocket;
