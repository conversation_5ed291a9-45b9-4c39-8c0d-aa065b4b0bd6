import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { whatsappService } from '../whatsapp/service';
import { getWebSocketPort } from '../config/websocket';

export interface WebSocketMessage {
  type:
    | 'subscribe'
    | 'unsubscribe'
    | 'qr_code'
    | 'connection_status'
    | 'message'
    | 'error';
  sessionId?: string;
  data?: any;
}

export interface WebSocketClient {
  ws: WebSocket;
  sessionId?: string;
  subscriptions: Set<string>;
}

class WebSocketManager {
  private static instance: WebSocketManager | null = null; // Added for singleton
  private wss: WebSocketServer | null = null;
  private clients: Map<WebSocket, WebSocketClient> = new Map();
  private sessionSubscriptions: Map<string, Set<WebSocket>> = new Map();
  private isInitializing: boolean = false; // Guard against re-entrant initialization

  public initialize(port?: number): void {
    // Use provided port, or get from configuration
    const actualPort = port ?? getWebSocketPort();
    console.log(
      `[WebSocketManager] initialize called. Current wss is ${
        this.wss ? 'set' : 'null'
      }. isInitializing: ${this.isInitializing}`
    );
    if (this.wss) {
      // Check if the existing server is actually listening
      if (this.wss.address()) {
        console.log(
          `[WebSocketManager] WebSocket server already running and listening on port ${
            (this.wss.address() as any).port
          }.`
        );
        return;
      } else {
        console.log(
          '[WebSocketManager] Existing wss found but not listening. Attempting to re-initialize.'
        );
        // Potentially close the old one if it's in a weird state, though 'close' might be called already if it errored.
        try {
          this.wss.close();
        } catch (e) {
          console.warn(
            '[WebSocketManager] Error closing non-listening wss, proceeding with re-initialization:',
            e
          );
        }
        this.wss = null; // Allow re-initialization
      }
    }

    if (this.isInitializing) {
      console.log(
        '[WebSocketManager] Initialization already in progress, returning.'
      );
      return;
    }
    this.isInitializing = true;

    try {
      console.log(
        `[WebSocketManager] Attempting to create WebSocketServer on port ${actualPort}.`
      );
      const server = new WebSocketServer({
        port: actualPort,
        perMessageDeflate: false,
        // Fix for Docker WebSocket masking issues
        clientTracking: true,
        maxPayload: 100 * 1024 * 1024, // 100MB
        // Disable compression to avoid masking issues
        compression: 'DISABLED',
      });
      this.wss = server; // Assign immediately

      server.on('listening', () => {
        this.isInitializing = false; // Clear flag once successfully listening
        console.log(
          `[WebSocketManager] WebSocket server is listening on port ${actualPort}.`
        );
      });

      server.on('error', (error: NodeJS.ErrnoException) => {
        this.isInitializing = false; // Clear flag on error during setup
        console.error(
          '[WebSocketManager] WebSocketServer emitted error:',
          error
        );
        if (error.code === 'EADDRINUSE') {
          console.log(
            `[WebSocketManager] Port ${actualPort} is in use (reported by 'error' event).`
          );
          // If this error occurs, the server didn't start.
          // We should ensure wss is nullified if it's this instance that failed.
          if (this.wss === server) {
            this.wss = null;
          }
        }
        // No re-throw here, as this is an async event.
        // The server is likely not operational if it hits a major error like EADDRINUSE here.
      });

      server.on('connection', ws => {
        console.log('[WebSocketManager] New WebSocket connection established');
        const client: WebSocketClient = {
          ws,
          subscriptions: new Set(),
        };

        this.clients.set(ws, client);

        ws.on('message', (data: Buffer) => {
          try {
            const message: WebSocketMessage = JSON.parse(data.toString());
            this.handleMessage(ws, message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
            this.sendError(ws, 'Invalid message format');
          }
        });

        ws.on('close', () => {
          console.log('WebSocket connection closed');
          this.handleDisconnection(ws);
        });

        ws.on('error', error => {
          console.error('WebSocket error:', error);
          this.handleDisconnection(ws);
        });

        // Send initial connection confirmation
        this.sendMessage(ws, {
          type: 'connection_status',
          data: { status: 'connected', timestamp: new Date().toISOString() },
        });
      });

      console.log(`WebSocket server started on port ${actualPort}`);
    } catch (error) {
      this.isInitializing = false; // Clear flag on synchronous error
      const nodeError = error as NodeJS.ErrnoException;
      console.error(
        '[WebSocketManager] Synchronous error during new WebSocketServer():',
        nodeError
      );
      if (nodeError.code === 'EADDRINUSE') {
        console.log(
          `[WebSocketManager] Port ${actualPort} is in use (caught synchronously during new WebSocketServer()). this.wss is currently ${
            this.wss === null ? 'null' : 'set'
          }.`
        );
        // If this.wss was assigned to the failing server instance, nullify it.
        // However, if the constructor throws, this.wss might not have been assigned yet, or assigned to the thing that threw.
        // The assignment `this.wss = server` is just above. If that line is reached and then an error is thrown by constructor,
        // this.wss would point to the problematic server.
        if (this.wss && typeof this.wss.address !== 'function') {
          // A bit of a guess if it's the problematic one
          this.wss = null;
        }
        // Do not re-throw, let the route handler know initialization might have failed if needed.
        // Or, re-throw a custom error if the API route needs to react.
        // For now, just log and return, assuming the server is not started.
        return;
      }
      // For other synchronous errors, re-throw to be caught by the caller in connect/route.ts
      console.error(
        '[WebSocketManager] Re-throwing synchronous error from initialize:',
        nodeError
      );
      throw nodeError;
    }
    // The 'isInitializing' flag is reset within 'listening' or 'error' event handlers for async path,
    // or in catch block for sync path.
  }

  private handleMessage(ws: WebSocket, message: WebSocketMessage): void {
    const client = this.clients.get(ws);
    if (!client) return;

    switch (message.type) {
      case 'subscribe':
        this.handleSubscribe(ws, message.sessionId);
        break;

      case 'unsubscribe':
        this.handleUnsubscribe(ws, message.sessionId);
        break;

      default:
        this.sendError(ws, `Unknown message type: ${message.type}`);
    }
  }

  private handleSubscribe(ws: WebSocket, sessionId?: string): void {
    if (!sessionId) {
      this.sendError(ws, 'Session ID is required for subscription');
      return;
    }

    const client = this.clients.get(ws);
    if (!client) return;

    // Add to client subscriptions
    client.subscriptions.add(sessionId);
    client.sessionId = sessionId;

    // Add to session subscriptions
    if (!this.sessionSubscriptions.has(sessionId)) {
      this.sessionSubscriptions.set(sessionId, new Set());
    }
    this.sessionSubscriptions.get(sessionId)!.add(ws);

    // Set up WhatsApp event listener for this session
    whatsappService.onEvent(sessionId, (event: string, data: any) => {
      this.broadcastToSession(sessionId, {
        type: event as any,
        sessionId,
        data,
      });
    });

    // Send current session status
    const session = whatsappService.getSession(sessionId);
    if (session) {
      this.sendMessage(ws, {
        type: 'connection_status',
        sessionId,
        data: {
          status: session.status,
          qrCode: session.qrCode,
          phoneNumber: session.phoneNumber,
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log(`Client subscribed to session: ${sessionId}`);
  }

  private handleUnsubscribe(ws: WebSocket, sessionId?: string): void {
    const client = this.clients.get(ws);
    if (!client) return;

    if (sessionId) {
      // Remove from specific session
      client.subscriptions.delete(sessionId);
      const sessionSubs = this.sessionSubscriptions.get(sessionId);
      if (sessionSubs) {
        sessionSubs.delete(ws);
        if (sessionSubs.size === 0) {
          this.sessionSubscriptions.delete(sessionId);
        }
      }
    } else {
      // Remove from all sessions
      for (const subSessionId of client.subscriptions) {
        const sessionSubs = this.sessionSubscriptions.get(subSessionId);
        if (sessionSubs) {
          sessionSubs.delete(ws);
          if (sessionSubs.size === 0) {
            this.sessionSubscriptions.delete(subSessionId);
          }
        }
      }
      client.subscriptions.clear();
    }

    console.log(`Client unsubscribed from session: ${sessionId || 'all'}`);
  }

  private handleDisconnection(ws: WebSocket): void {
    const client = this.clients.get(ws);
    if (client) {
      console.log(
        `[WebSocket] Client disconnected from session: ${
          client.sessionId || 'unknown'
        }`
      );

      // Remove from all session subscriptions
      for (const sessionId of client.subscriptions) {
        const sessionSubs = this.sessionSubscriptions.get(sessionId);
        if (sessionSubs) {
          sessionSubs.delete(ws);
          if (sessionSubs.size === 0) {
            console.log(
              `[WebSocket] No more clients for session ${sessionId}, but keeping WhatsApp session active`
            );
            this.sessionSubscriptions.delete(sessionId);
            // NOTE: We do NOT destroy the WhatsApp session here
            // The WhatsApp session should remain active even without WebSocket clients
          }
        }
      }
    }

    this.clients.delete(ws);
  }

  public broadcastToSession(
    sessionId: string,
    message: WebSocketMessage
  ): void {
    const subscribers = this.sessionSubscriptions.get(sessionId);
    if (!subscribers) return;

    const messageStr = JSON.stringify(message);

    for (const ws of subscribers) {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(messageStr);
        } catch (error) {
          console.error('Error sending message to client:', error);
          this.handleDisconnection(ws);
        }
      }
    }
  }

  private sendMessage(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending message:', error);
      }
    }
  }

  private sendError(ws: WebSocket, error: string): void {
    this.sendMessage(ws, {
      type: 'error',
      data: { error, timestamp: new Date().toISOString() },
    });
  }

  public getConnectedClients(): number {
    return this.clients.size;
  }

  public getSessionSubscribers(sessionId: string): number {
    return this.sessionSubscriptions.get(sessionId)?.size || 0;
  }

  public close(): void {
    console.log('[WebSocketManager] close() called.'); // Added log
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.clients.clear();
    this.sessionSubscriptions.clear();
    // WebSocketManager.instance = null; // Important: Don't nullify the global instance here during close
  }

  // Added for singleton
  public static getInstance(): WebSocketManager {
    if (process.env.NODE_ENV === 'development') {
      // In development, use a global variable to preserve the instance,
      // allowing HMR to work correctly.
      // @ts-ignore
      if (!global.webSocketManagerInstance) {
        // @ts-ignore
        global.webSocketManagerInstance = new WebSocketManager();
      }
      // @ts-ignore
      return global.webSocketManagerInstance;
    }

    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }
}

// Singleton instance
// export const webSocketManager = new WebSocketManager(); // Old way
export const webSocketManager = WebSocketManager.getInstance(); // New way
