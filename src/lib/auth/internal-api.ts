import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { JWT } from 'next-auth/jwt';

/**
 * Utility for handling internal API authentication using NextAuth JWT tokens
 * This ensures secure communication between internal API routes while leveraging
 * the existing NextAuth middleware authentication system.
 */

/**
 * Get JWT token from request for internal API calls
 * @param request - The incoming NextRequest
 * @returns JWT token or null if not authenticated
 */
export async function getInternalApiToken(request: NextRequest): Promise<JWT | null> {
  try {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    if (!token) {
      console.warn('[Internal API] No valid JWT token found in request');
      return null;
    }

    // Validate token has required fields
    if (!token.email || !token.sub) {
      console.warn('[Internal API] JWT token missing required fields (email, sub)');
      return null;
    }

    return token;
  } catch (error) {
    console.error('[Internal API] Error getting JWT token:', error);
    return null;
  }
}

/**
 * Create headers for internal API calls with JWT authentication
 * @param request - The originating NextRequest
 * @returns Headers object with authentication
 */
export async function createInternalApiHeaders(request: NextRequest): Promise<HeadersInit> {
  const token = await getInternalApiToken(request);
  
  if (!token) {
    throw new Error('No valid authentication token available for internal API call');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${JSON.stringify(token)}`,
    'X-Internal-API': 'true', // Mark as internal call
  };
}

/**
 * Extract JWT token from Authorization header in receiving API route
 * @param request - The incoming NextRequest
 * @returns JWT token or null if invalid
 */
export function extractInternalApiToken(request: NextRequest): JWT | null {
  try {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const tokenString = authHeader.replace('Bearer ', '');
    const token = JSON.parse(tokenString) as JWT;

    // Validate token structure
    if (!token.email || !token.sub) {
      console.warn('[Internal API] Invalid token structure in Authorization header');
      return null;
    }

    return token;
  } catch (error) {
    console.error('[Internal API] Error extracting token from Authorization header:', error);
    return null;
  }
}

/**
 * Validate that the request is an internal API call with valid authentication
 * @param request - The incoming NextRequest
 * @returns JWT token if valid, null if invalid
 */
export function validateInternalApiCall(request: NextRequest): JWT | null {
  // Check if marked as internal call
  const isInternal = request.headers.get('X-Internal-API') === 'true';
  
  if (!isInternal) {
    console.warn('[Internal API] Request not marked as internal API call');
    return null;
  }

  // Extract and validate token
  const token = extractInternalApiToken(request);
  
  if (!token) {
    console.warn('[Internal API] No valid token in internal API call');
    return null;
  }

  return token;
}

/**
 * Make an authenticated internal API call
 * @param request - The originating NextRequest
 * @param url - The internal API endpoint URL
 * @param options - Fetch options (method, body, etc.)
 * @returns Promise<Response>
 */
export async function makeInternalApiCall(
  request: NextRequest,
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  try {
    const headers = await createInternalApiHeaders(request);
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Internal API call failed: ${response.status} ${errorText}`);
    }

    return response;
  } catch (error) {
    console.error('[Internal API] Error making internal API call:', error);
    throw error;
  }
}

/**
 * Utility to get user info from JWT token
 * @param token - JWT token
 * @returns User information
 */
export function getUserFromToken(token: JWT) {
  return {
    id: token.sub!,
    email: token.email!,
    name: token.name || undefined,
  };
}

/**
 * Check if user has permission for the operation (can be extended)
 * @param token - JWT token
 * @returns boolean indicating if user has permission
 */
export function hasPermission(token: JWT): boolean {
  // Basic permission check - user must have valid email
  // This can be extended with role-based permissions later
  return !!token.email && !!token.sub;
}
