import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export interface AuthenticatedRequest extends NextRequest {
  user: {
    id: string;
    email: string;
    name?: string;
  };
}

export type AuthenticatedHandler = (
  request: AuthenticatedRequest,
  context?: any
) => Promise<NextResponse>;

/**
 * Higher-order function that wraps API route handlers with authentication
 * Usage:
 * export const POST = withAuth(async (request) => {
 *   // request.user is guaranteed to exist
 *   const userId = request.user.id;
 *   // ... your handler logic
 * });
 */
export function withAuth(handler: AuthenticatedHandler) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      // Check authentication
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Add user info to request
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = {
        id: session.user.id,
        email: session.user.email!,
        name: session.user.name || undefined,
      };

      // Call the original handler with authenticated request
      return await handler(authenticatedRequest, context);
    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Wrapper for routes that should be public (no authentication required)
 * This is mainly for documentation purposes and consistency
 */
export function withoutAuth(handler: (request: NextRequest, context?: any) => Promise<NextResponse>) {
  return handler;
}
