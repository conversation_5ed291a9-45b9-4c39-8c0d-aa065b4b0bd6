/**
 * WebSocket Configuration
 * Centralized configuration for WebSocket connections with environment variable support
 */

export const WEBSOCKET_CONFIG = {
  // Default WebSocket port
  DEFAULT_PORT: 3002,
  
  // Get WebSocket port from environment or use default
  getPort(): number {
    const envPort = process.env.WEBSOCKET_PORT || process.env.NEXT_PUBLIC_WEBSOCKET_PORT;
    if (envPort) {
      const port = parseInt(envPort, 10);
      if (!isNaN(port) && port > 0 && port <= 65535) {
        return port;
      }
      console.warn(`Invalid WebSocket port in environment: ${envPort}, using default: ${this.DEFAULT_PORT}`);
    }
    return this.DEFAULT_PORT;
  },

  // Get WebSocket URL for client-side connections
  getClientUrl(): string {
    const port = this.getPort();
    const protocol = typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
    return `${protocol}//${host}:${port}`;
  },

  // Get WebSocket URL for server-side connections
  getServerUrl(): string {
    const port = this.getPort();
    return `ws://localhost:${port}`;
  }
};

// Export individual functions for convenience
export const getWebSocketPort = () => WEBSOCKET_CONFIG.getPort();
export const getWebSocketClientUrl = () => WEBSOCKET_CONFIG.getClientUrl();
export const getWebSocketServerUrl = () => WEBSOCKET_CONFIG.getServerUrl();
