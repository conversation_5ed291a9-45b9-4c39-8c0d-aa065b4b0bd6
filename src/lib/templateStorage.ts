import { type SampleAgent } from './sampleData';

export interface CustomTemplate {
  id: string;
  name: string;
  description: string;
  data: SampleAgent['data'];
  createdAt: string;
  version: string;
}

const STORAGE_KEY = 'customTemplates';

/**
 * Get all custom templates from localStorage
 */
export function getCustomTemplates(): CustomTemplate[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error reading custom templates from localStorage:', error);
    return [];
  }
}

/**
 * Save a new custom template to localStorage
 */
export function saveCustomTemplate(templateData: SampleAgent['data'], name: string, description: string): CustomTemplate {
  const newTemplate: CustomTemplate = {
    id: `custom-${Date.now()}`,
    name,
    description,
    data: templateData,
    createdAt: new Date().toISOString(),
    version: '1.0.0'
  };

  try {
    const existingTemplates = getCustomTemplates();
    const updatedTemplates = [...existingTemplates, newTemplate];
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedTemplates));
    return newTemplate;
  } catch (error) {
    console.error('Error saving custom template to localStorage:', error);
    throw new Error('Failed to save template');
  }
}

/**
 * Delete a custom template from localStorage
 */
export function deleteCustomTemplate(templateId: string): void {
  try {
    const existingTemplates = getCustomTemplates();
    const updatedTemplates = existingTemplates.filter(t => t.id !== templateId);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedTemplates));
  } catch (error) {
    console.error('Error deleting custom template from localStorage:', error);
    throw new Error('Failed to delete template');
  }
}

/**
 * Update an existing custom template in localStorage
 */
export function updateCustomTemplate(templateId: string, updates: Partial<CustomTemplate>): void {
  try {
    const existingTemplates = getCustomTemplates();
    const updatedTemplates = existingTemplates.map(t => 
      t.id === templateId ? { ...t, ...updates } : t
    );
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedTemplates));
  } catch (error) {
    console.error('Error updating custom template in localStorage:', error);
    throw new Error('Failed to update template');
  }
}

/**
 * Check if localStorage is available
 */
export function isStorageAvailable(): boolean {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}
