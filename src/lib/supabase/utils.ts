import { createClient } from './client';
import { createClient as createServerClient } from './server';
import type { Database } from './types';

// Type aliases for easier use
export type Tables<T extends keyof Database['public']['Tables']> =
  Database['public']['Tables'][T]['Row'];
export type Enums<T extends keyof Database['public']['Enums']> =
  Database['public']['Enums'][T];

// Client-side utilities
export const supabase = createClient();

// Server-side utilities
export const getServerSupabase = () => createServerClient();

// User profile utilities
export async function getUserProfile(userId: string) {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }

  return data;
}

export async function createUserProfile(profile: {
  user_id: string;
  email: string;
  name?: string;
  avatar_url?: string;
}) {
  const { data, error } = await supabase
    .from('user_profiles')
    .insert(profile)
    .select()
    .single();

  if (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }

  return data;
}

export async function updateUserProfile(
  userId: string,
  updates: Partial<Tables<'user_profiles'>>
) {
  const { data, error } = await supabase
    .from('user_profiles')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('user_id', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }

  return data;
}

// Assistant configuration utilities
export async function getAssistantConfigurations(userId: string) {
  const { data, error } = await supabase
    .from('assistant_configurations')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching assistant configurations:', error);
    return [];
  }

  return data;
}

export async function createAssistantConfiguration(config: {
  user_id: string;
  name: string;
  description?: string;
  model_config: Record<string, unknown>;
  prompt_template: string;
}) {
  const { data, error } = await supabase
    .from('assistant_configurations')
    .insert(config)
    .select()
    .single();

  if (error) {
    console.error('Error creating assistant configuration:', error);
    throw error;
  }

  return data;
}

// Product utilities
export async function getProducts(userId: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching products:', error);
    return [];
  }

  return data;
}

export async function createProduct(product: {
  user_id: string;
  name: string;
  description: string;
  price?: number;
  category?: string;
  metadata?: Record<string, unknown>;
  embedding?: string;
}) {
  const { data, error } = await supabase
    .from('products')
    .insert(product)
    .select()
    .single();

  if (error) {
    console.error('Error creating product:', error);
    throw error;
  }

  return data;
}

// Vector search utilities
export async function searchProducts(
  userId: string,
  queryEmbedding: number[],
  threshold: number = 0.78,
  limit: number = 10
) {
  const { data, error } = await supabase.rpc('match_user_products', {
    user_id_param: userId,
    query_embedding: `[${queryEmbedding.join(',')}]`,
    match_threshold: threshold,
    match_count: limit,
  });

  if (error) {
    console.error('Error searching products:', error);
    return [];
  }

  return data;
}

// Transaction logging utilities
export async function logTransaction(log: {
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  metadata?: Record<string, unknown>;
}) {
  const { error } = await supabase.from('transaction_logs').insert(log);

  if (error) {
    console.error('Error logging transaction:', error);
  }
}

// WhatsApp session utilities
export async function getWhatsAppSessions(userId: string) {
  const { data, error } = await supabase
    .from('whatsapp_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching WhatsApp sessions:', error);
    return [];
  }

  return data;
}

export async function createWhatsAppSession(session: {
  user_id: string;
  session_id: string;
  status: string;
  phone_number?: string;
  qr_code?: string;
  session_data?: Record<string, unknown>;
}) {
  const { data, error } = await supabase
    .from('whatsapp_sessions')
    .insert(session)
    .select()
    .single();

  if (error) {
    console.error('Error creating WhatsApp session:', error);
    throw error;
  }

  return data;
}

export async function updateWhatsAppSession(
  sessionId: string,
  updates: Partial<Tables<'whatsapp_sessions'>>
) {
  const { data, error } = await supabase
    .from('whatsapp_sessions')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('session_id', sessionId)
    .select()
    .single();

  if (error) {
    console.error('Error updating WhatsApp session:', error);
    throw error;
  }

  return data;
}
