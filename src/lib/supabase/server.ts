import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Singleton instance for server-side operations
let serverClient: ReturnType<typeof createSupabaseClient> | null = null;

export function createClient() {
  if (!serverClient) {
    // Use server-only SUPABASE_URL to avoid build-time baking of environment variables
    const supabaseUrl =
      process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase environment variables are not configured');
    }

    serverClient = createSupabaseClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });
  }

  return serverClient;
}
