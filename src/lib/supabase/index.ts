// Supabase client exports
export { createClient } from './client';
export { createClient as createServerClient } from './server';
export { updateSession } from './middleware';

// Type exports
export type { Database, Tables, Enums } from './types';

// Utility exports
export * from './utils';

// Re-export commonly used types
export type {
  UserProfile,
  AssistantConfiguration,
  Product,
  TransactionLog,
  WhatsAppSession,
} from './types';

// Type aliases for convenience
export type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
export type AssistantConfiguration = Database['public']['Tables']['assistant_configurations']['Row'];
export type Product = Database['public']['Tables']['products']['Row'];
export type TransactionLog = Database['public']['Tables']['transaction_logs']['Row'];
export type WhatsAppSession = Database['public']['Tables']['whatsapp_sessions']['Row'];
