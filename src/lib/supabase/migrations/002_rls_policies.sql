-- Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE assistant_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE transaction_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_sessions ENABLE ROW LEVEL SECURITY;

-- User Profiles RLS Policies
-- Users can only see and modify their own profile
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own profile" ON user_profiles
    FOR DELETE USING (auth.uid()::text = user_id);

-- Assistant Configurations RLS Policies
-- Users can only access their own assistant configurations
CREATE POLICY "Users can view own assistant configurations" ON assistant_configurations
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own assistant configurations" ON assistant_configurations
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own assistant configurations" ON assistant_configurations
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own assistant configurations" ON assistant_configurations
    FOR DELETE USING (auth.uid()::text = user_id);

-- Products RLS Policies
-- Users can only access their own products
CREATE POLICY "Users can view own products" ON products
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own products" ON products
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own products" ON products
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own products" ON products
    FOR DELETE USING (auth.uid()::text = user_id);

-- Transaction Logs RLS Policies
-- Users can only view their own transaction logs (read-only for users)
CREATE POLICY "Users can view own transaction logs" ON transaction_logs
    FOR SELECT USING (auth.uid()::text = user_id);

-- Service role can insert transaction logs
CREATE POLICY "Service role can insert transaction logs" ON transaction_logs
    FOR INSERT WITH CHECK (true);

-- WhatsApp Sessions RLS Policies
-- Users can only access their own WhatsApp sessions
CREATE POLICY "Users can view own whatsapp sessions" ON whatsapp_sessions
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own whatsapp sessions" ON whatsapp_sessions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own whatsapp sessions" ON whatsapp_sessions
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own whatsapp sessions" ON whatsapp_sessions
    FOR DELETE USING (auth.uid()::text = user_id);
