import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Use service role key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface AssistantConfigurationDB {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  business_setup: any;
  sales_assistant: any;
  product_knowledge: any;
  status: 'draft' | 'configured' | 'active';
  created_at: string;
  updated_at: string;
}

export class AssistantConfigurationDatabase {
  /**
   * Create a new assistant configuration
   */
  static async createConfiguration(data: {
    userId: string;
    name: string;
    description?: string;
    businessSetup: any;
    salesAssistant: any;
    productKnowledge: any;
    status?: 'draft' | 'configured' | 'active';
  }): Promise<AssistantConfigurationDB | null> {
    try {
      const { data: config, error } = await supabase
        .from('assistant_configurations')
        .insert({
          user_id: data.userId,
          name: data.name,
          description: data.description,
          business_setup: data.businessSetup,
          sales_assistant: data.salesAssistant,
          product_knowledge: data.productKnowledge,
          status: data.status || 'draft',
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating assistant configuration:', error);
        return null;
      }

      return config;
    } catch (error) {
      console.error('Error creating assistant configuration:', error);
      return null;
    }
  }

  /**
   * Get an assistant configuration by ID
   */
  static async getConfiguration(configId: string): Promise<AssistantConfigurationDB | null> {
    try {
      const { data: config, error } = await supabase
        .from('assistant_configurations')
        .select('*')
        .eq('id', configId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        console.error('Error getting assistant configuration:', error);
        return null;
      }

      return config;
    } catch (error) {
      console.error('Error getting assistant configuration:', error);
      return null;
    }
  }

  /**
   * Get all assistant configurations for a user
   */
  static async getUserConfigurations(userId: string): Promise<AssistantConfigurationDB[]> {
    try {
      const { data: configs, error } = await supabase
        .from('assistant_configurations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting user configurations:', error);
        return [];
      }

      return configs || [];
    } catch (error) {
      console.error('Error getting user configurations:', error);
      return [];
    }
  }

  /**
   * Update an assistant configuration
   */
  static async updateConfiguration(
    configId: string,
    updates: Partial<Omit<AssistantConfigurationDB, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<AssistantConfigurationDB | null> {
    try {
      const { data: config, error } = await supabase
        .from('assistant_configurations')
        .update(updates)
        .eq('id', configId)
        .select()
        .single();

      if (error) {
        console.error('Error updating assistant configuration:', error);
        return null;
      }

      return config;
    } catch (error) {
      console.error('Error updating assistant configuration:', error);
      return null;
    }
  }

  /**
   * Delete an assistant configuration
   */
  static async deleteConfiguration(configId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('assistant_configurations')
        .delete()
        .eq('id', configId);

      if (error) {
        console.error('Error deleting assistant configuration:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting assistant configuration:', error);
      return false;
    }
  }

  /**
   * Get configurations by status
   */
  static async getConfigurationsByStatus(
    userId: string,
    status: 'draft' | 'configured' | 'active'
  ): Promise<AssistantConfigurationDB[]> {
    try {
      const { data: configs, error } = await supabase
        .from('assistant_configurations')
        .select('*')
        .eq('user_id', userId)
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting configurations by status:', error);
        return [];
      }

      return configs || [];
    } catch (error) {
      console.error('Error getting configurations by status:', error);
      return [];
    }
  }

  /**
   * Clone an assistant configuration
   */
  static async cloneConfiguration(
    configId: string,
    newName: string,
    userId: string
  ): Promise<AssistantConfigurationDB | null> {
    try {
      // Get the original configuration
      const original = await this.getConfiguration(configId);
      if (!original) {
        return null;
      }

      // Create a new configuration with the same data
      return await this.createConfiguration({
        userId,
        name: newName,
        description: `Clone of ${original.name}`,
        businessSetup: original.business_setup,
        salesAssistant: original.sales_assistant,
        productKnowledge: original.product_knowledge,
        status: 'draft',
      });
    } catch (error) {
      console.error('Error cloning assistant configuration:', error);
      return null;
    }
  }

  /**
   * Get configuration with WhatsApp session info
   */
  static async getConfigurationWithSession(configId: string): Promise<{
    config: AssistantConfigurationDB;
    whatsappSession?: any;
  } | null> {
    try {
      const { data, error } = await supabase
        .from('assistant_configurations')
        .select(`
          *,
          whatsapp_sessions (
            id,
            session_id,
            phone_number,
            status,
            last_activity
          )
        `)
        .eq('id', configId)
        .single();

      if (error) {
        console.error('Error getting configuration with session:', error);
        return null;
      }

      return {
        config: data,
        whatsappSession: data.whatsapp_sessions?.[0] || null,
      };
    } catch (error) {
      console.error('Error getting configuration with session:', error);
      return null;
    }
  }

  /**
   * Search configurations by name or description
   */
  static async searchConfigurations(
    userId: string,
    searchTerm: string
  ): Promise<AssistantConfigurationDB[]> {
    try {
      const { data: configs, error } = await supabase
        .from('assistant_configurations')
        .select('*')
        .eq('user_id', userId)
        .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error searching configurations:', error);
        return [];
      }

      return configs || [];
    } catch (error) {
      console.error('Error searching configurations:', error);
      return [];
    }
  }
}
