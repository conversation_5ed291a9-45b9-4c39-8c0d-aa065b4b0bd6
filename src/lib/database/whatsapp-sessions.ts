import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Use service role key for server-side operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface WhatsAppSessionDB {
  id: string;
  session_id: string;
  phone_number?: string;
  user_id: string;
  assistant_config_id?: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'qr_required';
  qr_code?: string;
  connection_data?: any;
  credentials?: any;
  last_activity: string;
  created_at: string;
  updated_at: string;
}

export interface WhatsAppMessageDB {
  id: string;
  session_id: string;
  message_id?: string;
  from_number: string;
  to_number: string;
  message_text?: string;
  message_type: 'text' | 'image' | 'document' | 'audio' | 'video';
  direction: 'incoming' | 'outgoing';
  timestamp: string;
  processed_by_assistant: boolean;
  assistant_response_id?: string;
  metadata?: any;
  created_at: string;
}

export interface AssistantConfigurationDB {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  business_setup: any;
  sales_assistant: any;
  product_knowledge: any;
  status: 'draft' | 'configured' | 'active';
  whatsapp_session_id?: string;
  created_at: string;
  updated_at: string;
}

export class WhatsAppSessionDatabase {
  /**
   * Create a new WhatsApp session in the database
   */
  static async createSession(data: {
    sessionId: string;
    userId: string;
    assistantConfigId?: string;
    phoneNumber?: string;
  }): Promise<WhatsAppSessionDB | null> {
    try {
      const { data: session, error } = await supabase
        .from('whatsapp_sessions')
        .insert({
          session_id: data.sessionId,
          user_id: data.userId,
          assistant_config_id: data.assistantConfigId,
          phone_number: data.phoneNumber,
          status: 'connecting',
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating WhatsApp session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error creating WhatsApp session:', error);
      return null;
    }
  }

  /**
   * Get a WhatsApp session by session ID
   */
  static async getSession(
    sessionId: string
  ): Promise<WhatsAppSessionDB | null> {
    try {
      const { data: session, error } = await supabase
        .from('whatsapp_sessions')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        console.error('Error getting WhatsApp session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error getting WhatsApp session:', error);
      return null;
    }
  }

  /**
   * Update a WhatsApp session
   */
  static async updateSession(
    sessionId: string,
    updates: Partial<
      Omit<WhatsAppSessionDB, 'id' | 'session_id' | 'created_at' | 'updated_at'>
    >
  ): Promise<WhatsAppSessionDB | null> {
    try {
      const { data: session, error } = await supabase
        .from('whatsapp_sessions')
        .update({
          ...updates,
          last_activity: new Date().toISOString(),
        })
        .eq('session_id', sessionId)
        .select()
        .single();

      if (error) {
        console.error('Error updating WhatsApp session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error updating WhatsApp session:', error);
      return null;
    }
  }

  /**
   * Delete a WhatsApp session
   */
  static async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('whatsapp_sessions')
        .delete()
        .eq('session_id', sessionId);

      if (error) {
        console.error('Error deleting WhatsApp session:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting WhatsApp session:', error);
      return false;
    }
  }

  /**
   * Get all sessions for a user
   */
  static async getUserSessions(userId: string): Promise<WhatsAppSessionDB[]> {
    try {
      const { data: sessions, error } = await supabase
        .from('whatsapp_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting user sessions:', error);
        return [];
      }

      return sessions || [];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }

  /**
   * Store a WhatsApp message
   */
  static async storeMessage(data: {
    sessionId: string;
    messageId?: string;
    fromNumber: string;
    toNumber: string;
    messageText?: string;
    messageType?: 'text' | 'image' | 'document' | 'audio' | 'video';
    direction: 'incoming' | 'outgoing';
    timestamp: Date;
    processedByAssistant?: boolean;
    assistantResponseId?: string;
    metadata?: any;
  }): Promise<WhatsAppMessageDB | null> {
    try {
      // Get the session database ID
      const session = await this.getSession(data.sessionId);
      if (!session) {
        console.error('Session not found for message storage');
        return null;
      }

      const { data: message, error } = await supabase
        .from('whatsapp_messages')
        .insert({
          session_id: session.id,
          message_id: data.messageId,
          from_number: data.fromNumber,
          to_number: data.toNumber,
          message_text: data.messageText,
          message_type: data.messageType || 'text',
          direction: data.direction,
          timestamp: data.timestamp.toISOString(),
          processed_by_assistant: data.processedByAssistant || false,
          assistant_response_id: data.assistantResponseId,
          metadata: data.metadata,
        })
        .select()
        .single();

      if (error) {
        console.error('Error storing WhatsApp message:', error);
        return null;
      }

      return message;
    } catch (error) {
      console.error('Error storing WhatsApp message:', error);
      return null;
    }
  }

  /**
   * Get messages for a session
   */
  static async getSessionMessages(
    sessionId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<WhatsAppMessageDB[]> {
    try {
      // Get the session database ID
      const session = await this.getSession(sessionId);
      if (!session) {
        return [];
      }

      const { data: messages, error } = await supabase
        .from('whatsapp_messages')
        .select('*')
        .eq('session_id', session.id)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error getting session messages:', error);
        return [];
      }

      return messages || [];
    } catch (error) {
      console.error('Error getting session messages:', error);
      return [];
    }
  }

  /**
   * Get all connected sessions from database
   */
  static async getConnectedSessions(): Promise<WhatsAppSessionDB[]> {
    try {
      const { data: sessions, error } = await supabase
        .from('whatsapp_sessions')
        .select('*')
        .eq('status', 'connected')
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error getting connected sessions:', error);
        return [];
      }

      return sessions || [];
    } catch (error) {
      console.error('Error getting connected sessions:', error);
      return [];
    }
  }

  /**
   * Clean up old sessions
   */
  static async cleanupOldSessions(maxAgeHours: number = 24): Promise<number> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - maxAgeHours);

      const { data, error } = await supabase
        .from('whatsapp_sessions')
        .delete()
        .lt('last_activity', cutoffTime.toISOString())
        .neq('status', 'connected')
        .select('id');

      if (error) {
        console.error('Error cleaning up old sessions:', error);
        return 0;
      }

      return data?.length || 0;
    } catch (error) {
      console.error('Error cleaning up old sessions:', error);
      return 0;
    }
  }
}
