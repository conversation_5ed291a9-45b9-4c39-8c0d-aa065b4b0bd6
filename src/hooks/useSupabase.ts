'use client';

import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import {
  getUserProfile,
  createUserProfile,
  updateUserProfile,
  getAssistantConfigurations,
  getProducts,
  getWhatsAppSessions,
  type UserProfile,
  type AssistantConfiguration,
  type Product,
  type WhatsAppSession,
} from '@/lib/supabase';

export function useUserProfile() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProfile() {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let userProfile = await getUserProfile(user.id);

        // Create profile if it doesn't exist
        if (!userProfile && user.email) {
          userProfile = await createUserProfile({
            user_id: user.id,
            email: user.email,
            name: user.name || null,
            avatar_url: user.image || null,
          });
        }

        setProfile(userProfile);
      } catch (err) {
        console.error('Error fetching user profile:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch profile');
      } finally {
        setLoading(false);
      }
    }

    fetchProfile();
  }, [user]);

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user?.id) return;

    try {
      setError(null);
      const updatedProfile = await updateUserProfile(user.id, updates);
      setProfile(updatedProfile);
      return updatedProfile;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
      throw err;
    }
  };

  return {
    profile,
    loading,
    error,
    updateProfile,
  };
}

export function useAssistantConfigurations() {
  const { user } = useAuth();
  const [configurations, setConfigurations] = useState<AssistantConfiguration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchConfigurations() {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const configs = await getAssistantConfigurations(user.id);
        setConfigurations(configs);
      } catch (err) {
        console.error('Error fetching assistant configurations:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch configurations');
      } finally {
        setLoading(false);
      }
    }

    fetchConfigurations();
  }, [user]);

  const refreshConfigurations = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const configs = await getAssistantConfigurations(user.id);
      setConfigurations(configs);
    } catch (err) {
      console.error('Error refreshing configurations:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh configurations');
    }
  };

  return {
    configurations,
    loading,
    error,
    refreshConfigurations,
  };
}

export function useProducts() {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProducts() {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const userProducts = await getProducts(user.id);
        setProducts(userProducts);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch products');
      } finally {
        setLoading(false);
      }
    }

    fetchProducts();
  }, [user]);

  const refreshProducts = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const userProducts = await getProducts(user.id);
      setProducts(userProducts);
    } catch (err) {
      console.error('Error refreshing products:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh products');
    }
  };

  return {
    products,
    loading,
    error,
    refreshProducts,
  };
}

export function useWhatsAppSessions() {
  const { user } = useAuth();
  const [sessions, setSessions] = useState<WhatsAppSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSessions() {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const userSessions = await getWhatsAppSessions(user.id);
        setSessions(userSessions);
      } catch (err) {
        console.error('Error fetching WhatsApp sessions:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch sessions');
      } finally {
        setLoading(false);
      }
    }

    fetchSessions();
  }, [user]);

  const refreshSessions = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const userSessions = await getWhatsAppSessions(user.id);
      setSessions(userSessions);
    } catch (err) {
      console.error('Error refreshing sessions:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh sessions');
    }
  };

  return {
    sessions,
    loading,
    error,
    refreshSessions,
  };
}
