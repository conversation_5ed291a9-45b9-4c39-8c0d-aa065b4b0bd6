'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const login = async (callbackUrl?: string) => {
    await signIn('google', { callbackUrl: callbackUrl || '/' });
  };

  const logout = async () => {
    await signOut({ callbackUrl: '/' });
  };

  const requireAuth = (redirectTo: string = '/login') => {
    if (status === 'loading') return false;
    if (!session) {
      router.push(redirectTo);
      return false;
    }
    return true;
  };

  return {
    user: session?.user,
    session,
    isLoading: status === 'loading',
    isAuthenticated: !!session,
    login,
    logout,
    requireAuth,
  };
}
