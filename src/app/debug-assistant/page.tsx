'use client';

import { useState } from 'react';

export default function DebugAssistantPage() {
  const [sessionId, setSessionId] = useState('test-session-' + Date.now());
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (title: string, data: any, success: boolean = true) => {
    setResults(prev => [...prev, {
      timestamp: new Date().toISOString(),
      title,
      data,
      success
    }]);
  };

  const testAssistantAPI = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Test 1: Enable Assistant
      addResult('Step 1: Enabling Assistant...', { sessionId }, true);
      
      const enableResponse = await fetch('/api/whatsapp/assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          assistantConfig: {
            salesAssistant: {
              name: 'TestBot',
              personality: 'friendly',
              tone: 'professional',
              objectives: ['Help customers']
            },
            businessSetup: {
              companyName: 'Test Company',
              industry: 'Technology',
              description: 'Test company'
            },
            productKnowledge: {
              products: [
                { name: 'Test Product', description: 'A test product', price: 100, category: 'Test' }
              ]
            }
          }
        })
      });

      const enableData = await enableResponse.json();
      addResult('Step 1 Result: Enable Assistant', enableData, enableResponse.ok);

      if (!enableResponse.ok) {
        setLoading(false);
        return;
      }

      // Test 2: Check Assistant Status
      const statusResponse = await fetch(`/api/whatsapp/assistant?sessionId=${sessionId}`);
      const statusData = await statusResponse.json();
      addResult('Step 2: Check Assistant Status', statusData, statusResponse.ok);

      // Test 3: Test Message Processing
      const testMessage = {
        id: 'test-msg-' + Date.now(),
        from: '<EMAIL>',
        to: '<EMAIL>',
        message: 'Hello, I need help with your products',
        timestamp: new Date().toISOString(),
        type: 'incoming'
      };

      addResult('Step 3: Processing Test Message...', testMessage, true);

      const processResponse = await fetch('/api/whatsapp/process-message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          message: testMessage
        })
      });

      const processData = await processResponse.json();
      addResult('Step 3 Result: Process Message', processData, processResponse.ok);

      // Test 4: Test Chat API Directly
      addResult('Step 4: Testing Chat API Directly...', {}, true);

      const chatResponse = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'Hello, I need help with your products',
          assistantConfig: {
            name: 'TestBot',
            personality: 'friendly',
            tone: 'professional',
            objectives: ['Help customers']
          },
          businessInfo: {
            companyName: 'Test Company',
            industry: 'Technology',
            description: 'Test company'
          },
          products: [
            { name: 'Test Product', description: 'A test product', price: 100, category: 'Test' }
          ]
        })
      });

      const chatData = await chatResponse.json();
      addResult('Step 4 Result: Chat API', chatData, chatResponse.ok);

    } catch (error) {
      addResult('Error', { error: error instanceof Error ? error.message : 'Unknown error' }, false);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            WhatsApp Assistant Debug Tool
          </h1>

          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Session ID:
              </label>
              <input
                type="text"
                value={sessionId}
                onChange={(e) => setSessionId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div className="flex space-x-4">
              <button
                onClick={testAssistantAPI}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {loading ? 'Testing...' : 'Run Full Test'}
              </button>
              
              <button
                onClick={clearResults}
                disabled={loading}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                Clear Results
              </button>
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Test Results:
            </h2>

            {results.length === 0 && (
              <p className="text-gray-500 dark:text-gray-400">
                No tests run yet. Click "Run Full Test" to start debugging.
              </p>
            )}

            {results.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  result.success
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className={`font-semibold ${
                    result.success
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {result.title}
                  </h3>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                <pre className={`text-sm overflow-x-auto ${
                  result.success
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            ))}
          </div>

          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Debug Instructions:
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>1. This tool tests the assistant API endpoints step by step</li>
              <li>2. It will enable an assistant, check status, process a test message, and test the chat API</li>
              <li>3. Check each step for errors to identify where the issue occurs</li>
              <li>4. Green results = success, Red results = error</li>
              <li>5. Look at the console logs for additional debugging information</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
