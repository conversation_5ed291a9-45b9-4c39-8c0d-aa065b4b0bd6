'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';

interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: string;
  type: 'incoming' | 'outgoing';
}

interface AssistantStatus {
  sessionId: string;
  assistantEnabled: boolean;
  assistantConfig: any;
  activeSessions: string[];
}

export default function AssistantConnectedPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('sessionId');

  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [assistantStatus, setAssistantStatus] = useState<AssistantStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    if (!sessionId) {
      setError('No session ID provided');
      setLoading(false);
      return;
    }

    // Check assistant status
    checkAssistantStatus();

    // Connect to WebSocket for real-time messages
    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [sessionId]);

  const checkAssistantStatus = async () => {
    try {
      const response = await fetch(`/api/whatsapp/assistant?sessionId=${sessionId}`);
      const data = await response.json();

      if (response.ok) {
        setAssistantStatus(data);
      } else {
        setError(data.error || 'Failed to check assistant status');
      }
    } catch (error) {
      console.error('Error checking assistant status:', error);
      setError('Failed to connect to assistant service');
    } finally {
      setLoading(false);
    }
  };

  const processMessageWithAssistant = async (sessionId: string | null, message: WhatsAppMessage) => {
    if (!sessionId) return;

    try {
      console.log('Processing message with assistant API:', message);

      const response = await fetch('/api/whatsapp/process-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          message: {
            id: message.id,
            from: message.from,
            to: message.to,
            message: message.message,
            timestamp: message.timestamp,
            type: message.type,
          },
        }),
      });

      const result = await response.json();
      console.log('Assistant processing result:', result);

      if (!response.ok) {
        console.error('Failed to process message with assistant:', result.error);
      }
    } catch (error) {
      console.error('Error processing message with assistant:', error);
    }
  };

  const connectWebSocket = () => {
    try {
      const ws = new WebSocket('ws://localhost:3002');
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        // Subscribe to session events
        ws.send(JSON.stringify({
          type: 'subscribe',
          sessionId: sessionId
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('WebSocket message:', data);

          switch (data.type) {
            case 'message_received': {
              const incomingMessage = data.data;

              // Add message to display
              setMessages(prev => {
                // Avoid duplicates
                const exists = prev.some(msg => msg.id === incomingMessage.id);
                if (exists) return prev;
                return [...prev, incomingMessage].sort((a, b) =>
                  new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
                );
              });

              // Process message with assistant
              console.log('Received message, processing with assistant:', incomingMessage);
              processMessageWithAssistant(sessionId, incomingMessage);
              break;
            }
            case 'message_sent':
              setMessages(prev => {
                // Avoid duplicates
                const exists = prev.some(msg => msg.id === data.data.id);
                if (exists) return prev;
                return [...prev, data.data].sort((a, b) =>
                  new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
                );
              });
              break;
            case 'disconnected':
              setError('WhatsApp session disconnected');
              break;
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error connecting WebSocket:', error);
    }
  };

  const handleDisableAssistant = async () => {
    try {
      const response = await fetch(`/api/whatsapp/assistant?sessionId=${sessionId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setAssistantStatus(prev => prev ? { ...prev, assistantEnabled: false } : null);
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to disable assistant');
      }
    } catch (error) {
      console.error('Error disabling assistant:', error);
      setError('Failed to disable assistant');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading assistant status...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Error</h1>
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.href = '/builder'}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Back to Builder
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-6xl mr-4">🤖</div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Assistant Connected!
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  Your AI sales assistant is live and handling WhatsApp messages
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-600 dark:text-green-400 font-medium">Live</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Assistant Status */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Assistant Status
            </h2>

            {assistantStatus && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    assistantStatus.assistantEnabled
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {assistantStatus.assistantEnabled ? 'Active' : 'Inactive'}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-300">Session ID:</span>
                  <span className="text-sm font-mono text-gray-900 dark:text-white">
                    {sessionId?.slice(-8)}...
                  </span>
                </div>

                {assistantStatus.assistantConfig && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Assistant:</span>
                      <span className="text-gray-900 dark:text-white">
                        {assistantStatus.assistantConfig.salesAssistant?.name || 'AI Assistant'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Company:</span>
                      <span className="text-gray-900 dark:text-white">
                        {assistantStatus.assistantConfig.businessSetup?.companyName || 'N/A'}
                      </span>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={handleDisableAssistant}
                    className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                  >
                    Disable Assistant
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Live Messages */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Live Messages
              </h2>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {messages.length} messages
              </span>
            </div>

            <div className="h-96 overflow-y-auto space-y-3 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <div className="text-4xl mb-2">💬</div>
                  <p>Waiting for messages...</p>
                  <p className="text-sm mt-2">Send a message to your WhatsApp number to see the assistant in action!</p>
                </div>
              ) : (
                messages.map((msg, index) => (
                  <div
                    key={msg.id || index}
                    className={`flex ${msg.type === 'outgoing' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        msg.type === 'outgoing'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white'
                      }`}
                    >
                      <div className="text-sm mb-1">
                        <strong>
                          {msg.type === 'outgoing' ? 'Assistant' : msg.from.split('@')[0]}:
                        </strong>
                      </div>
                      <div>{msg.message}</div>
                      <div className="text-xs opacity-75 mt-1">
                        {new Date(msg.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 text-center">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              🎉 Your Assistant is Live!
            </h3>
            <p className="text-blue-800 dark:text-blue-200 mb-4">
              Send messages to your WhatsApp number and watch your AI assistant respond automatically.
            </p>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => window.location.href = '/'}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Go to Dashboard
              </button>
              <button
                onClick={() => window.location.href = '/builder'}
                className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                Create Another Assistant
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
