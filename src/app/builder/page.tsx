'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import UserDropdown from '@/components/ui/UserDropdown';

// Wizard Steps
import BusinessSetup from '@/components/wizard/BusinessSetup';
import SalesAssistant from '@/components/wizard/SalesAssistant';
import ProductKnowledge from '@/components/wizard/ProductKnowledge';
import GoLive from '@/components/wizard/GoLive';
import QuickStart from '@/components/wizard/QuickStart';
import OnboardingFlow from '@/components/wizard/OnboardingFlow';

// Types
interface WizardData {
  businessSetup: {
    companyName: string;
    industry: string;
    description: string;
    website?: string;
    assistantName?: string;
    assistantDescription?: string;
  };
  salesAssistant: {
    name: string;
    personality: string;
    tone: string;
    language: string;
    objectives: string[];
    customPrompt?: string;
  };
  productKnowledge: {
    products: Array<{
      name: string;
      description: string;
      price?: number;
      category?: string;
    }>;
    knowledgeBase?: string;
  };
  goLive: {
    whatsappConnected: boolean;
    testingCompleted: boolean;
    isLive: boolean;
  };
}

const WIZARD_STEPS = [
  {
    id: 1,
    title: 'Business Setup',
    description: 'Tell us about your business',
    icon: '🏢',
  },
  {
    id: 2,
    title: 'Sales Assistant',
    description: 'Configure your AI assistant',
    icon: '🤖',
  },
  {
    id: 3,
    title: 'Product Knowledge',
    description: 'Add your products & services',
    icon: '📦',
  },
  {
    id: 4,
    title: 'Go Live',
    description: 'Connect WhatsApp & test',
    icon: '🚀',
  },
];

export default function BuilderPage() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isQuickStartMode, setIsQuickStartMode] = useState(true); // Start with quickstart mode
  const [showOnboarding, setShowOnboarding] = useState(true); // Show onboarding for new users
  const [editingAssistantId, setEditingAssistantId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [wizardData, setWizardData] = useState<WizardData>({
    businessSetup: {
      companyName: '',
      industry: '',
      description: '',
      website: '',
      assistantName: '',
      assistantDescription: '',
    },
    salesAssistant: {
      name: '',
      personality: 'professional',
      tone: 'friendly',
      language: 'english',
      objectives: [],
      customPrompt: '',
    },
    productKnowledge: {
      products: [],
      knowledgeBase: '',
    },
    goLive: {
      whatsappConnected: false,
      testingCompleted: false,
      isLive: false,
    },
  });

  // Check for edit mode from URL parameters
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const editId = urlParams.get('edit');
      if (editId && editId !== editingAssistantId) {
        setEditingAssistantId(editId);
        loadAssistantForEditing(editId);
      }
    }
  }, [editingAssistantId]);



  // Load assistant data for editing
  const loadAssistantForEditing = async (assistantId: string) => {
    try {
      const response = await fetch(`/api/assistant/${assistantId}`);
      if (response.ok) {
        const data = await response.json();
        const config = data.configuration;

        setWizardData({
          businessSetup: {
            ...config.business_setup,
            assistantName: config.name,
            assistantDescription: config.description ?? '',
          },
          salesAssistant: config.sales_assistant,
          productKnowledge: config.product_knowledge,
          goLive: {
            whatsappConnected: false,
            testingCompleted: false,
            isLive: false,
          },
        });

        setIsQuickStartMode(false);
        setShowOnboarding(false);
        setCurrentStep(1);
      }
    } catch (error) {
      console.error('Error loading assistant for editing:', error);
      setSaveError('Failed to load assistant for editing');
    }
  };

  // Helper function to get assistant name with fallback logic
  const getAssistantName = (): string => {
    return wizardData.businessSetup.assistantName ??
           wizardData.businessSetup.companyName ??
           'Untitled Assistant';
  };

  // Helper function to get assistant description with fallback logic
  const getAssistantDescription = (): string => {
    return wizardData.businessSetup.assistantDescription ??
           `AI assistant for ${wizardData.businessSetup.companyName}`;
  };

  // Save assistant function
  const saveAssistant = async (status: 'draft' | 'configured' = 'draft') => {
    if (!isAuthenticated || !user?.id) {
      setSaveError('You must be signed in to save assistants');
      return;
    }

    setSaving(true);
    setSaveError(null);

    try {
      const assistantData = {
        name: getAssistantName(),
        description: getAssistantDescription(),
        businessSetup: wizardData.businessSetup,
        salesAssistant: wizardData.salesAssistant,
        productKnowledge: wizardData.productKnowledge,
        status,
      };

      let response;
      if (editingAssistantId) {
        // Update existing assistant
        response = await fetch(`/api/assistant/${editingAssistantId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(assistantData),
        });
      } else {
        // Create new assistant
        response = await fetch('/api/assistant', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(assistantData),
        });
      }

      if (response.ok) {
        const data = await response.json();
        if (!editingAssistantId) {
          setEditingAssistantId(data.configuration.id);
          // Update URL to reflect editing mode
          window.history.replaceState({}, '', `/builder?edit=${data.configuration.id}`);
        }
        return data.configuration;
      } else {
        const errorData = await response.json();
        setSaveError(errorData.error ?? 'Failed to save assistant');
        return null;
      }
    } catch (error) {
      console.error('Error saving assistant:', error);
      setSaveError('Failed to save assistant');
      return null;
    } finally {
      setSaving(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  // Authentication check
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Authentication Required
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Please sign in to access the Sales Assistant Builder.
          </p>
          <Link
            href="/login"
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  const handleStepComplete = (stepData: WizardData[keyof WizardData]) => {
    setWizardData(prev => ({
      ...prev,
      [getStepKey(currentStep)]: stepData,
    }));

    if (currentStep < WIZARD_STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleStepBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else if (currentStep === 1) {
      // Go back to QuickStart mode
      setIsQuickStartMode(true);
      setCurrentStep(1);
    }
  };

  // Quickstart handlers
  const handleQuickStartSelect = (sampleData: any) => {
    setWizardData(sampleData);
    setIsQuickStartMode(false);
    setCurrentStep(4); // Skip to Go Live step for quickstart
  };

  const handleQuickStartCustomize = (sampleData: any) => {
    setWizardData(sampleData);
    setIsQuickStartMode(false);
    setCurrentStep(1); // Start from first step for customization
  };

  const handleCustomStart = () => {
    setIsQuickStartMode(false);
    setCurrentStep(1); // Start from first step for custom
  };

  // Onboarding handlers
  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  const handleOnboardingSkip = () => {
    setShowOnboarding(false);
  };

  // Handle back to assistants
  const handleBackToAssistants = () => {
    router.push('/assistants');
  };



  const getStepKey = (step: number): keyof WizardData => {
    switch (step) {
      case 1: return 'businessSetup';
      case 2: return 'salesAssistant';
      case 3: return 'productKnowledge';
      case 4: return 'goLive';
      default: return 'businessSetup';
    }
  };

  const renderCurrentStep = () => {
    const stepData = wizardData[getStepKey(currentStep)];

    switch (currentStep) {
      case 1:
        return (
          <BusinessSetup
            data={stepData}
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            onSave={saveAssistant}
            saving={saving}
            saveError={saveError}
          />
        );
      case 2:
        return (
          <SalesAssistant
            data={stepData}
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            onSave={saveAssistant}
            saving={saving}
            saveError={saveError}
            assistantName={wizardData.businessSetup.assistantName}
          />
        );
      case 3:
        return (
          <ProductKnowledge
            data={stepData}
            onComplete={handleStepComplete}
            onBack={handleStepBack}
            onSave={saveAssistant}
            saving={saving}
            saveError={saveError}
          />
        );
      case 4:
        return (
          <GoLive
            data={stepData}
            wizardData={wizardData}
            onComplete={handleStepComplete}
            onBack={handleStepBack}
          />
        );
      default:
        return null;
    }
  };

  // Show QuickStart mode if enabled
  if (isQuickStartMode) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <QuickStart
              onSelectSample={handleQuickStartSelect}
              onCustomStart={handleCustomStart}
              onCustomizeTemplate={handleQuickStartCustomize}
              onBack={handleBackToAssistants}
            />

            {/* User Info */}
            <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
              Signed in as {user?.name} ({user?.email})
            </div>
          </div>
        </div>

        {/* Onboarding Flow */}
        {showOnboarding && (
          <OnboardingFlow
            onComplete={handleOnboardingComplete}
            onSkip={handleOnboardingSkip}
          />
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <nav className="container mx-auto px-4 py-6">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-gray-900 dark:text-white">
            SalesFlow AI
          </Link>
          <div className="flex items-center gap-4">
            <Link
              href="/assistants"
              className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              My Assistants
            </Link>

            {/* User Menu */}
            <UserDropdown user={user} />
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {editingAssistantId ? 'Edit Assistant' : 'Sales Assistant Builder'}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {editingAssistantId
                ? 'Update your AI-powered WhatsApp sales assistant'
                : 'Create your AI-powered WhatsApp sales assistant in 4 simple steps'
              }
            </p>
            {editingAssistantId && (
              <div className="mt-2">
                <Link
                  href="/assistants"
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                >
                  ← Back to Assistants
                </Link>
              </div>
            )}
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {WIZARD_STEPS.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full text-lg font-medium ${
                    currentStep >= step.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}>
                    {currentStep > step.id ? '✓' : step.icon}
                  </div>
                  <div className="ml-3 hidden sm:block">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {step.description}
                    </p>
                  </div>
                  {index < WIZARD_STEPS.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      currentStep > step.id
                        ? 'bg-blue-600'
                        : 'bg-gray-200 dark:bg-gray-700'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Current Step Content */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            {renderCurrentStep()}
          </div>

          {/* User Info */}
          <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
            Signed in as {user?.name} ({user?.email})
          </div>
        </div>
      </div>
    </div>
  );
}
