'use client';

import { useState } from 'react';

interface TestResult {
  status?: string;
  message?: string;
  response?: string;
  model?: string;
  error?: string;
}

export default function TestOpenAI() {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [chatResponse, setChatResponse] = useState('');
  const [chatLoading, setChatLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/chat', {
        method: 'GET',
      });
      const data = await response.json();
      setTestResult(data);
    } catch {
      setTestResult({ error: 'Failed to connect to API' });
    }
    setLoading(false);
  };

  const testChat = async () => {
    if (!chatMessage.trim()) return;

    setChatLoading(true);
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: chatMessage,
          assistantConfig: {
            name: 'TestBot',
            personality: 'friendly',
            tone: 'casual',
            objectives: ['Test OpenAI integration'],
          },
          businessInfo: {
            companyName: 'Test Company',
            industry: 'Technology',
            description: 'A test company for OpenAI integration',
          },
          products: [
            {
              name: 'Test Product',
              description: 'A sample product for testing',
              price: 99,
              category: 'Software',
            },
          ],
        }),
      });

      const data = await response.json();
      setChatResponse(data.response ?? data.error ?? 'No response');
    } catch {
      setChatResponse('Failed to get response');
    }
    setChatLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          OpenAI API Test
        </h1>

        {/* Connection Test */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            1. Test API Connection
          </h2>
          <button
            onClick={testConnection}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
          >
            {loading ? 'Testing...' : 'Test Connection'}
          </button>

          {testResult && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Result:</h3>
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Chat Test */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            2. Test Chat with Wizard Configuration
          </h2>

          <div className="space-y-4">
            <div>
              <label
                htmlFor="chat-message"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Test Message:
              </label>
              <input
                id="chat-message"
                type="text"
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && testChat()}
                placeholder="Type a message to test the AI assistant..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <button
              onClick={testChat}
              disabled={chatLoading || !chatMessage.trim()}
              className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
            >
              {chatLoading ? 'Getting Response...' : 'Send Message'}
            </button>

            {chatResponse && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">AI Response:</h3>
                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {chatResponse}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Sample Test Messages */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            3. Sample Test Messages
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              'Hello, what can you help me with?',
              'Tell me about your products',
              'What are your prices?',
              'How can I contact you?',
              'What makes your company special?',
              'Do you offer customer support?',
            ].map((message) => (
              <button
                key={message}
                onClick={() => setChatMessage(message)}
                className="p-3 text-left bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                {message}
              </button>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
            How to Test
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 dark:text-blue-200">
            <li>First, click &ldquo;Test Connection&rdquo; to verify OpenAI API is working</li>
            <li>Then, type a message or click a sample message</li>
            <li>Click &ldquo;Send Message&rdquo; to test the AI assistant</li>
            <li>The response should be contextual based on the test configuration</li>
            <li>Try different messages to see how the AI responds</li>
          </ol>
        </div>

        {/* Back to Builder */}
        <div className="text-center mt-8">
          <a
            href="/builder"
            className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            ← Back to Wizard Builder
          </a>
        </div>
      </div>
    </div>
  );
}
