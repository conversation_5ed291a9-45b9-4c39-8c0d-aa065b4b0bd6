import { NextRequest, NextResponse } from 'next/server';
import { openai } from '@/lib/openai';
import { createClient } from '@/lib/supabase/server';

// Embeddings configuration
const EMBEDDINGS_CONFIG = {
  model: 'text-embedding-3-small', // OpenAI's latest embedding model
  dimensions: 1536, // Default dimensions for text-embedding-3-small
  batchSize: 100, // Process embeddings in batches
};

// Helper function to generate embeddings
async function generateEmbeddings(texts: string[]): Promise<number[][]> {
  try {
    const response = await openai.embeddings.create({
      model: EMBEDDINGS_CONFIG.model,
      input: texts,
      dimensions: EMBEDDINGS_CONFIG.dimensions,
    });

    return response.data.map(item => item.embedding);
  } catch (error) {
    console.error('Failed to generate embeddings:', error);
    throw error;
  }
}

// Helper function to store product embeddings
async function storeProductEmbeddings(
  userId: string,
  products: Array<{
    name: string;
    description: string;
    price?: number;
    category?: string;
    metadata?: Record<string, unknown>;
  }>
) {
  const supabase = createClient();

  // Generate embeddings for product descriptions
  const descriptions = products.map(p => `${p.name}: ${p.description}`);
  const embeddings = await generateEmbeddings(descriptions);

  // Prepare data for insertion
  const productData = products.map((product, index) => ({
    user_id: userId,
    name: product.name,
    description: product.description,
    price: product.price,
    category: product.category,
    metadata: product.metadata,
    embedding: embeddings[index],
  }));

  // Insert products with embeddings
  const { data, error } = await supabase
    .from('products')
    .insert(productData)
    .select();

  if (error) {
    throw new Error(`Failed to store products: ${error.message}`);
  }

  return data;
}

// Helper function to update product embedding
async function updateProductEmbedding(
  productId: string,
  name: string,
  description: string
) {
  const supabase = createClient();

  // Generate new embedding
  const [embedding] = await generateEmbeddings([`${name}: ${description}`]);

  // Update product with new embedding
  const { data, error } = await supabase
    .from('products')
    .update({
      name,
      description,
      embedding,
      updated_at: new Date().toISOString(),
    })
    .eq('id', productId)
    .select();

  if (error) {
    throw new Error(`Failed to update product: ${error.message}`);
  }

  return data[0];
}

// POST endpoint for generating embeddings
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, data } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'generate_product_embeddings': {
        if (!data?.products || !Array.isArray(data.products)) {
          return NextResponse.json(
            { error: 'Products array is required' },
            { status: 400 }
          );
        }

        const storedProducts = await storeProductEmbeddings(
          userId,
          data.products
        );

        return NextResponse.json({
          success: true,
          message: `Generated embeddings for ${storedProducts.length} products`,
          products: storedProducts,
          model: EMBEDDINGS_CONFIG.model,
          dimensions: EMBEDDINGS_CONFIG.dimensions,
        });
      }

      case 'update_product_embedding': {
        const { productId, name, description } = data;

        if (!productId || !name || !description) {
          return NextResponse.json(
            { error: 'Product ID, name, and description are required' },
            { status: 400 }
          );
        }

        const updatedProduct = await updateProductEmbedding(
          productId,
          name,
          description
        );

        return NextResponse.json({
          success: true,
          message: 'Product embedding updated successfully',
          product: updatedProduct,
          model: EMBEDDINGS_CONFIG.model,
        });
      }

      case 'generate_text_embedding': {
        const { text } = data;

        if (!text) {
          return NextResponse.json(
            { error: 'Text is required' },
            { status: 400 }
          );
        }

        const [embedding] = await generateEmbeddings([text]);

        return NextResponse.json({
          success: true,
          embedding,
          model: EMBEDDINGS_CONFIG.model,
          dimensions: embedding.length,
        });
      }

      case 'batch_generate_embeddings': {
        const { texts } = data;

        if (!texts || !Array.isArray(texts)) {
          return NextResponse.json(
            { error: 'Texts array is required' },
            { status: 400 }
          );
        }

        // Process in batches to avoid API limits with parallel processing
        const embeddings = [];
        const batches = [];

        // Create batches
        for (let i = 0; i < texts.length; i += EMBEDDINGS_CONFIG.batchSize) {
          const batch = texts.slice(i, i + EMBEDDINGS_CONFIG.batchSize);
          batches.push(batch);
        }

        // Process batches in parallel (with concurrency limit to respect rate limits)
        const maxConcurrentBatches = 3; // Limit concurrent requests to avoid rate limits
        const batchPromises = [];

        for (let i = 0; i < batches.length; i += maxConcurrentBatches) {
          const concurrentBatches = batches.slice(i, i + maxConcurrentBatches);
          const batchResults = await Promise.all(
            concurrentBatches.map(batch => generateEmbeddings(batch))
          );
          batchPromises.push(...batchResults);
        }

        // Flatten results
        for (const batchResult of batchPromises) {
          embeddings.push(...batchResult);
        }

        return NextResponse.json({
          success: true,
          embeddings,
          count: embeddings.length,
          model: EMBEDDINGS_CONFIG.model,
          dimensions: embeddings[0]?.length ?? EMBEDDINGS_CONFIG.dimensions,
        });
      }

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] Embeddings API Error:`,
      error
    );

    if (error instanceof Error) {
      const errorMessage = error.message;

      if (errorMessage.includes('insufficient_quota')) {
        return NextResponse.json(
          { error: 'OpenAI API quota exceeded. Please check your billing.' },
          { status: 402 }
        );
      }

      if (errorMessage.includes('invalid_api_key')) {
        return NextResponse.json(
          { error: 'Invalid OpenAI API key configuration.' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        {
          error: `Embeddings API Error: ${errorMessage}`,
          requestId,
          timestamp,
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}

// GET endpoint for embeddings info and testing
export async function GET() {
  try {
    // Test embeddings generation
    const testText = 'This is a test for embeddings generation.';
    const [testEmbedding] = await generateEmbeddings([testText]);

    return NextResponse.json({
      status: 'success',
      message: 'Embeddings API is working!',
      config: {
        model: EMBEDDINGS_CONFIG.model,
        dimensions: EMBEDDINGS_CONFIG.dimensions,
        batchSize: EMBEDDINGS_CONFIG.batchSize,
      },
      test: {
        input: testText,
        embeddingLength: testEmbedding.length,
        embeddingPreview: testEmbedding.slice(0, 5), // First 5 dimensions
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] Embeddings API Test Error:`,
      error
    );

    return NextResponse.json(
      {
        status: 'error',
        message: 'Embeddings API connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}
