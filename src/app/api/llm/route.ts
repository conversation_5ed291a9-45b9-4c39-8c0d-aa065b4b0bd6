import { NextRequest, NextResponse } from 'next/server';
import {
  createChatCompletion,
  buildSystemPrompt,
  OPENAI_CONFIG,
} from '@/lib/openai';
import { createClient } from '@/lib/supabase/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Rate limiting storage (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMIT = {
  maxRequests: 100, // requests per window
  windowMs: 60 * 1000, // 1 minute
};

// Helper function for rate limiting
function checkRateLimit(clientId: string): {
  allowed: boolean;
  remaining: number;
  resetTime: number;
} {
  const now = Date.now();
  const key = clientId;

  let bucket = rateLimitStore.get(key);

  if (!bucket || now > bucket.resetTime) {
    bucket = {
      count: 0,
      resetTime: now + RATE_LIMIT.windowMs,
    };
  }

  if (bucket.count >= RATE_LIMIT.maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: bucket.resetTime,
    };
  }

  bucket.count++;
  rateLimitStore.set(key, bucket);

  return {
    allowed: true,
    remaining: RATE_LIMIT.maxRequests - bucket.count,
    resetTime: bucket.resetTime,
  };
}

// Helper function to track token usage
async function trackTokenUsage(
  userId: string | null,
  model: string,
  promptTokens: number,
  completionTokens: number,
  totalTokens: number
) {
  try {
    const supabase = createClient();

    await supabase.from('transaction_logs').insert({
      user_id: userId,
      action: 'llm_request',
      resource_type: 'openai_api',
      resource_id: model,
      metadata: {
        model,
        prompt_tokens: promptTokens,
        completion_tokens: completionTokens,
        total_tokens: totalTokens,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Failed to track token usage:', error);
    // Don't throw - this shouldn't break the main request
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Get client IP for rate limiting
    const clientIp =
      request.headers.get('x-forwarded-for') ??
      request.headers.get('x-real-ip') ??
      'unknown';

    // Check rate limit
    const rateLimit = checkRateLimit(clientIp);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          resetTime: rateLimit.resetTime,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimit.resetTime.toString(),
          },
        }
      );
    }

    const body = await request.json();
    const { message, assistantConfig, businessInfo, products, options } = body;

    const userId = session?.user?.id; // Use authenticated user ID

    // Validate required fields
    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Build system prompt based on wizard configuration
    const systemPrompt = buildSystemPrompt(
      assistantConfig,
      businessInfo,
      products
    );

    // Call OpenAI API using centralized function with retry logic
    let completion;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        completion = await createChatCompletion(
          [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: message,
            },
          ],
          options
        );
        break; // Success, exit retry loop
      } catch (error: unknown) {
        retryCount++;

        // If it's a rate limit error from OpenAI, wait and retry
        if (
          (error as { status?: number })?.status === 429 &&
          retryCount < maxRetries
        ) {
          const waitTime = Math.pow(2, retryCount) * 1000; // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        // If it's not retryable or we've exhausted retries, throw
        throw error;
      }
    }

    if (!completion) {
      throw new Error('Failed to get completion after retries');
    }

    const response =
      completion.choices[0]?.message?.content ??
      'Sorry, I could not generate a response.';

    // Track token usage
    if (completion.usage) {
      await trackTokenUsage(
        userId ?? null,
        completion.model,
        completion.usage.prompt_tokens ?? 0,
        completion.usage.completion_tokens ?? 0,
        completion.usage.total_tokens ?? 0
      );
    }

    return NextResponse.json(
      {
        response,
        usage: completion.usage,
        model: completion.model,
        metadata: {
          timestamp: new Date().toISOString(),
          systemPromptLength: systemPrompt.length,
          rateLimitRemaining: rateLimit.remaining,
        },
      },
      {
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.maxRequests.toString(),
          'X-RateLimit-Remaining': rateLimit.remaining.toString(),
          'X-RateLimit-Reset': rateLimit.resetTime.toString(),
        },
      }
    );
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] LLM API Error:`,
      error
    );

    // Handle specific OpenAI errors
    if (error instanceof Error) {
      const errorMessage = error.message;

      if (errorMessage.includes('insufficient_quota')) {
        return NextResponse.json(
          { error: 'OpenAI API quota exceeded. Please check your billing.' },
          { status: 402 }
        );
      }

      if (errorMessage.includes('invalid_api_key')) {
        return NextResponse.json(
          { error: 'Invalid OpenAI API key configuration.' },
          { status: 401 }
        );
      }

      if (errorMessage.includes('model_not_found')) {
        return NextResponse.json(
          { error: 'Requested model not available.' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          error: `LLM API Error: ${errorMessage}`,
          requestId,
          timestamp,
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}

// Test endpoint to verify OpenAI connection
export async function GET() {
  try {
    // Simple test to verify OpenAI API key works
    const completion = await createChatCompletion(
      [
        {
          role: 'user',
          content: 'Say "OpenAI LLM API is working!" in a friendly way.',
        },
      ],
      { maxTokens: 50 }
    );

    return NextResponse.json({
      status: 'success',
      message: 'OpenAI LLM API is connected and working!',
      response: completion.choices[0]?.message?.content,
      model: completion.model,
      config: {
        defaultModel: OPENAI_CONFIG.model,
        maxTokens: OPENAI_CONFIG.maxTokens,
        temperature: OPENAI_CONFIG.temperature,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] LLM API Test Error:`,
      error
    );

    return NextResponse.json(
      {
        status: 'error',
        message: 'OpenAI LLM API connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}
