import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import type { NextAuthOptions } from 'next-auth';

// Simple email whitelist - only explicit emails allowed
const ALLOWED_EMAILS =
  process.env.ALLOWED_EMAILS?.split(',').map(email =>
    email.trim().toLowerCase()
  ) || [];

function isEmailAllowed(email: string): boolean {
  if (!email) return false;

  // Fail-secure: if no emails configured, reject all
  if (ALLOWED_EMAILS.length === 0) {
    console.log('Sign-in rejected: No emails configured in ALLOWED_EMAILS');
    return false;
  }

  // Check if email is in whitelist (case insensitive)
  const normalizedEmail = email.toLowerCase().trim();
  return ALLOWED_EMAILS.includes(normalizedEmail);
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  pages: {
    signIn: '/login',
    error: '/unauthorized',
  },
  callbacks: {
    async jwt({ token, account, user }) {
      // Persist the OAuth access_token to the token right after signin
      if (account) {
        token.accessToken = account.access_token;
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      // Send properties to the client
      if (session.user && token.id) {
        session.user.id = token.id;
        session.accessToken = token.accessToken;
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      // Simple email whitelist security check
      if (!user.email) {
        console.log('Sign-in rejected: No email provided');
        return false;
      }

      if (!isEmailAllowed(user.email)) {
        console.log(
          `Sign-in rejected: Email ${user.email} not in ALLOWED_EMAILS whitelist`
        );
        throw new Error('AccessDenied');
      }

      console.log(
        `Sign-in approved: Email ${user.email} is in ALLOWED_EMAILS whitelist`
      );
      return true;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
