import { NextRequest, NextResponse } from 'next/server';
import {
  AVAILABLE_MODELS,
  OPENAI_CONFIG,
  DEFAULT_PROMPT_TEMPLATES,
  getPromptTemplate,
  getPromptTemplatesByCategory,
  type OpenAIModel,
  type PromptTemplate,
} from '@/lib/openai';
import { createClient } from '@/lib/supabase/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Configuration interface for model selection and settings
export interface OpenAIConfiguration {
  model: OpenAIModel;
  maxTokens: number;
  temperature: number;
  promptTemplate?: string;
  customSettings?: Record<string, unknown>;
}

// GET endpoint for retrieving configuration options
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const category = searchParams.get('category') as PromptTemplate['category'];
    const templateId = searchParams.get('templateId');

    switch (action) {
      case 'models': {
        return NextResponse.json({
          success: true,
          models: AVAILABLE_MODELS,
          currentModel: OPENAI_CONFIG.model,
          defaultConfig: {
            model: OPENAI_CONFIG.model,
            maxTokens: OPENAI_CONFIG.maxTokens,
            temperature: OPENAI_CONFIG.temperature,
          },
        });
      }

      case 'templates': {
        let templates = DEFAULT_PROMPT_TEMPLATES;

        if (category) {
          templates = getPromptTemplatesByCategory(category);
        }

        return NextResponse.json({
          success: true,
          templates,
          categories: ['sales', 'support', 'general', 'custom'],
          totalCount: DEFAULT_PROMPT_TEMPLATES.length,
        });
      }

      case 'template': {
        if (!templateId) {
          return NextResponse.json(
            { error: 'Template ID is required' },
            { status: 400 }
          );
        }

        const template = getPromptTemplate(templateId);
        if (!template) {
          return NextResponse.json(
            { error: 'Template not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          template,
        });
      }

      case 'user_config': {
        // Get user-specific configuration from database
        const supabase = createClient();
        const { data: configs, error } = await supabase
          .from('assistant_configurations')
          .select('*')
          .eq('user_id', session?.user?.id)
          .eq('is_active', true);

        if (error) {
          throw new Error(`Failed to fetch user config: ${error.message}`);
        }

        return NextResponse.json({
          success: true,
          configurations: configs,
          count: configs?.length ?? 0,
        });
      }

      default: {
        // Return all configuration options
        return NextResponse.json({
          success: true,
          models: AVAILABLE_MODELS,
          templates: DEFAULT_PROMPT_TEMPLATES,
          currentConfig: {
            model: OPENAI_CONFIG.model,
            maxTokens: OPENAI_CONFIG.maxTokens,
            temperature: OPENAI_CONFIG.temperature,
          },
          categories: ['sales', 'support', 'general', 'custom'],
        });
      }
    }
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] Config API Error:`,
      error
    );

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}

// POST endpoint for saving configuration
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const body = await request.json();
    const { action, configuration } = body;
    const userId = session?.user?.id; // Use authenticated user ID

    const supabase = createClient();

    switch (action) {
      case 'save_assistant_config': {
        const {
          name,
          description,
          modelConfig,
          promptTemplate,
          isActive = true,
        } = configuration;

        if (!name || !modelConfig) {
          return NextResponse.json(
            { error: 'Name and model configuration are required' },
            { status: 400 }
          );
        }

        // Deactivate other configurations if this one is set as active
        if (isActive) {
          await supabase
            .from('assistant_configurations')
            .update({ is_active: false })
            .eq('user_id', userId);
        }

        // Insert new configuration
        const { data, error } = await supabase
          .from('assistant_configurations')
          .insert({
            user_id: userId,
            name,
            description,
            model_config: modelConfig,
            prompt_template: promptTemplate,
            is_active: isActive,
          })
          .select();

        if (error) {
          throw new Error(`Failed to save configuration: ${error.message}`);
        }

        return NextResponse.json({
          success: true,
          message: 'Configuration saved successfully',
          configuration: data[0],
        });
      }

      case 'update_assistant_config': {
        const { configId, updates } = configuration;

        if (!configId) {
          return NextResponse.json(
            { error: 'Configuration ID is required' },
            { status: 400 }
          );
        }

        // If setting as active, deactivate others first
        if (updates.is_active) {
          await supabase
            .from('assistant_configurations')
            .update({ is_active: false })
            .eq('user_id', userId);
        }

        const { data, error } = await supabase
          .from('assistant_configurations')
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq('id', configId)
          .eq('user_id', userId)
          .select();

        if (error) {
          throw new Error(`Failed to update configuration: ${error.message}`);
        }

        return NextResponse.json({
          success: true,
          message: 'Configuration updated successfully',
          configuration: data[0],
        });
      }

      case 'delete_assistant_config': {
        const { configId } = configuration;

        if (!configId) {
          return NextResponse.json(
            { error: 'Configuration ID is required' },
            { status: 400 }
          );
        }

        const { error } = await supabase
          .from('assistant_configurations')
          .delete()
          .eq('id', configId)
          .eq('user_id', userId);

        if (error) {
          throw new Error(`Failed to delete configuration: ${error.message}`);
        }

        return NextResponse.json({
          success: true,
          message: 'Configuration deleted successfully',
        });
      }

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);

    console.error(
      `[${timestamp}] [Request ID: ${requestId}] Config API Error:`,
      error
    );

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal server error',
        requestId,
        timestamp,
      },
      { status: 500 }
    );
  }
}
