import { NextRequest, NextResponse } from 'next/server';
import { createChatCompletion, buildSystemPrompt } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, assistantConfig, businessInfo, products, options } = body;

    // Validate required fields
    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Build system prompt based on wizard configuration
    const systemPrompt = buildSystemPrompt(
      assistantConfig,
      businessInfo,
      products
    );

    // Call OpenAI API using centralized function
    const completion = await createChatCompletion(
      [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: message,
        },
      ],
      options
    );

    const response =
      completion.choices[0]?.message?.content ??
      'Sorry, I could not generate a response.';

    return NextResponse.json({
      response,
      usage: completion.usage,
      model: completion.model,
    });
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);
    console.error(
      `[${timestamp}] [Request ID: ${requestId}] OpenAI API Error:`,
      error
    );

    if (error instanceof Error) {
      return NextResponse.json(
        { error: `OpenAI API Error: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Test endpoint to verify OpenAI connection
export async function GET() {
  try {
    // Simple test to verify OpenAI API key works using centralized function
    const completion = await createChatCompletion(
      [
        {
          role: 'user',
          content: 'Say "OpenAI API is working!" in a friendly way.',
        },
      ],
      { maxTokens: 50 }
    );

    return NextResponse.json({
      status: 'success',
      message: 'OpenAI API is connected and working!',
      response: completion.choices[0]?.message?.content,
      model: completion.model,
    });
  } catch (error) {
    const timestamp = new Date().toISOString();
    const requestId = Math.random().toString(36).substring(2, 15);
    console.error(
      `[${timestamp}] [Request ID: ${requestId}] OpenAI API Test Error:`,
      error
    );

    return NextResponse.json(
      {
        status: 'error',
        message: 'OpenAI API connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
