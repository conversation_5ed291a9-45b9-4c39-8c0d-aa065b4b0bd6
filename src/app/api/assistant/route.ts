import { NextRequest, NextResponse } from 'next/server';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as 'draft' | 'configured' | 'active' | null;
    const search = searchParams.get('search');

    let configurations;

    if (search) {
      configurations = await AssistantConfigurationDatabase.searchConfigurations(
        session.user.id,
        search
      );
    } else if (status) {
      configurations = await AssistantConfigurationDatabase.getConfigurationsByStatus(
        session.user.id,
        status
      );
    } else {
      configurations = await AssistantConfigurationDatabase.getUserConfigurations(
        session.user.id
      );
    }

    return NextResponse.json({
      success: true,
      configurations,
      count: configurations.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting assistant configurations:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to get assistant configurations',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      description, 
      businessSetup, 
      salesAssistant, 
      productKnowledge, 
      status = 'draft' 
    } = body;

    if (!name || !businessSetup || !salesAssistant || !productKnowledge) {
      return NextResponse.json(
        { error: 'Missing required fields: name, businessSetup, salesAssistant, productKnowledge' },
        { status: 400 }
      );
    }

    const configuration = await AssistantConfigurationDatabase.createConfiguration({
      userId: session.user.id,
      name,
      description,
      businessSetup,
      salesAssistant,
      productKnowledge,
      status,
    });

    if (!configuration) {
      return NextResponse.json(
        { error: 'Failed to create assistant configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      configuration,
      message: 'Assistant configuration created successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error creating assistant configuration:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to create assistant configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
