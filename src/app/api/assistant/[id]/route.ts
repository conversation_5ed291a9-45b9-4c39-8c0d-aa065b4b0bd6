import { NextRequest, NextResponse } from 'next/server';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: configId } = params;
    const configuration = await AssistantConfigurationDatabase.getConfiguration(
      configId
    );

    if (!configuration) {
      return NextResponse.json(
        { error: 'Assistant configuration not found' },
        { status: 404 }
      );
    }

    // Check if user owns this configuration
    if (configuration.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json({
      success: true,
      configuration,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting assistant configuration:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to get assistant configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: configId } = params;
    const body = await request.json();
    const {
      name,
      description,
      businessSetup,
      salesAssistant,
      productKnowledge,
      status,
    } = body;

    // First check if configuration exists and user owns it
    const existingConfig =
      await AssistantConfigurationDatabase.getConfiguration(configId);
    if (!existingConfig) {
      return NextResponse.json(
        { error: 'Assistant configuration not found' },
        { status: 404 }
      );
    }

    if (existingConfig.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Convert camelCase to snake_case for database
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (businessSetup !== undefined) updateData.business_setup = businessSetup;
    if (salesAssistant !== undefined)
      updateData.sales_assistant = salesAssistant;
    if (productKnowledge !== undefined)
      updateData.product_knowledge = productKnowledge;
    if (status !== undefined) updateData.status = status;

    const configuration =
      await AssistantConfigurationDatabase.updateConfiguration(
        configId,
        updateData
      );

    if (!configuration) {
      return NextResponse.json(
        { error: 'Failed to update assistant configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      configuration,
      message: 'Assistant configuration updated successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating assistant configuration:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update assistant configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: configId } = params;

    // First check if configuration exists and user owns it
    const existingConfig =
      await AssistantConfigurationDatabase.getConfiguration(configId);
    if (!existingConfig) {
      return NextResponse.json(
        { error: 'Assistant configuration not found' },
        { status: 404 }
      );
    }

    if (existingConfig.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const success = await AssistantConfigurationDatabase.deleteConfiguration(
      configId
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete assistant configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Assistant configuration deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting assistant configuration:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to delete assistant configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
