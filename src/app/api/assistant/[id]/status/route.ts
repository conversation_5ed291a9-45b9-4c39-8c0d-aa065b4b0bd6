import { NextRequest, NextResponse } from 'next/server';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: configId } = params;
    const body = await request.json();
    const { status } = body;

    // Validate status
    if (!status || !['draft', 'configured', 'active'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: draft, configured, active' },
        { status: 400 }
      );
    }

    // Check if configuration exists and user owns it
    const existingConfig =
      await AssistantConfigurationDatabase.getConfiguration(configId);
    if (!existingConfig) {
      return NextResponse.json(
        { error: 'Assistant configuration not found' },
        { status: 404 }
      );
    }

    if (existingConfig.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Update only the status
    const updatedConfiguration =
      await AssistantConfigurationDatabase.updateConfiguration(configId, {
        status,
      });

    if (!updatedConfiguration) {
      return NextResponse.json(
        { error: 'Failed to update assistant status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      configuration: updatedConfiguration,
      message: `Assistant status updated to ${status}`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating assistant status:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update assistant status',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
