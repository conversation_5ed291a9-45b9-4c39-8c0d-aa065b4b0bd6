import { NextRequest, NextResponse } from 'next/server';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: configId } = params;
    const body = await request.json();
    const { name } = body;

    // Get the original configuration
    const originalConfig =
      await AssistantConfigurationDatabase.getConfiguration(configId);
    if (!originalConfig) {
      return NextResponse.json(
        { error: 'Assistant configuration not found' },
        { status: 404 }
      );
    }

    // Check if user owns the original configuration
    if (originalConfig.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Create the cloned configuration
    const clonedConfig =
      await AssistantConfigurationDatabase.createConfiguration({
        userId: session.user.id,
        name: name ?? `${originalConfig.name} (Copy)`,
        description: originalConfig.description
          ? `${originalConfig.description} (Cloned)`
          : undefined,
        businessSetup: originalConfig.business_setup,
        salesAssistant: originalConfig.sales_assistant,
        productKnowledge: originalConfig.product_knowledge,
        status: 'draft', // Always start clones as draft
      });

    if (!clonedConfig) {
      return NextResponse.json(
        { error: 'Failed to clone assistant configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      configuration: clonedConfig,
      message: 'Assistant configuration cloned successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error cloning assistant configuration:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to clone assistant configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
