import { NextResponse } from 'next/server';
import { createClient as createServerClient } from '@/lib/supabase/server';
import { getServerAuthSession } from '@/lib/auth';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerAuthSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Test Supabase connection
    const supabase = createServerClient();

    // Test basic query
    const { error: testError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('Database test error:', testError);
      return NextResponse.json(
        {
          error: 'Database connection failed',
          details: testError.message,
        },
        { status: 500 }
      );
    }

    // Test user profile creation/retrieval
    const userId = session.user.id;
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    let profileData = profile;

    // Create profile if it doesn't exist
    if (profileError && profileError.code === 'PGRST116') {
      const { data: newProfile, error: createError } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          email: session.user.email!,
          name: session.user.name,
          avatar_url: session.user.image,
        })
        .select()
        .single();

      if (createError) {
        console.error('Profile creation error:', createError);
        return NextResponse.json(
          {
            error: 'Failed to create user profile',
            details: createError.message,
          },
          { status: 500 }
        );
      }

      profileData = newProfile;
    } else if (profileError) {
      console.error('Profile fetch error:', profileError);
      return NextResponse.json(
        {
          error: 'Failed to fetch user profile',
          details: profileError.message,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
        },
        profile: profileData,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
