import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';
import { webSocketManager } from '@/lib/websocket/manager';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, userId } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Initialize WebSocket server if not already running
    try {
      webSocketManager.initialize();
    } catch (error) {
      console.log('WebSocket server already initialized or error:', error);
    }

    // Check if session exists in memory
    let session = whatsappService.getSession(sessionId);

    if (!session) {
      console.log(
        `[API] Session ${sessionId} not found in memory, attempting to restore...`
      );

      // Try to restore from database and files
      try {
        session = await whatsappService.createSession(sessionId, userId);
        console.log(`[API] Session ${sessionId} restored successfully`);
      } catch (error) {
        console.error(`[API] Failed to restore session ${sessionId}:`, error);
        return NextResponse.json(
          { error: 'Failed to restore session' },
          { status: 500 }
        );
      }
    } else {
      console.log(
        `[API] Session ${sessionId} found in memory with status: ${session.status}`
      );
    }

    return NextResponse.json({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        qrCode: session.qrCode,
        phoneNumber: session.phoneNumber,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
      },
      message: 'Session restored successfully',
    });
  } catch (error) {
    console.error('Error restoring WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to restore WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const whatsappSession = whatsappService.getSession(sessionId);

    if (!whatsappSession) {
      return NextResponse.json(
        {
          success: false,
          exists: false,
          message: 'Session not found in memory',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      exists: true,
      session: {
        id: whatsappSession.id,
        status: whatsappSession.status,
        qrCode: whatsappSession.qrCode,
        phoneNumber: whatsappSession.phoneNumber,
        createdAt: whatsappSession.createdAt,
        lastActivity: whatsappSession.lastActivity,
      },
    });
  } catch (error) {
    console.error('Error checking WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to check WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
