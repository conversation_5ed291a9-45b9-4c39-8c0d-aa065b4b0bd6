import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';
import { webSocketManager } from '@/lib/websocket/manager';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, userId, assistantConfig, browserName } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Initialize WebSocket server if not already running
    try {
      webSocketManager.initialize();
    } catch (error) {
      // Server might already be running, which is fine
      console.log('WebSocket server already initialized or error:', error);
    }

    // Check if WhatsApp session is already connected and has a phone number
    const existingSession = whatsappService.getSession(sessionId);
    let phoneNumber: string | undefined = undefined;
    if (
      existingSession &&
      existingSession.status === 'connected' &&
      existingSession.phoneNumber
    ) {
      phoneNumber = existingSession.phoneNumber;
    }

    // Create WhatsApp session with user context
    const session = await whatsappService.createSession(
      sessionId,
      userId,
      assistantConfig,
      browserName // Pass custom browser name if provided
    );

    return NextResponse.json({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        qrCode: session.qrCode,
        phoneNumber: session.phoneNumber,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
      },
      message: 'WhatsApp session created successfully',
    });
  } catch (error) {
    console.error('Error creating WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const session = whatsappService.getSession(sessionId);

    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        qrCode: session.qrCode,
        phoneNumber: session.phoneNumber,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
      },
    });
  } catch (error) {
    console.error('Error getting WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to get WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
