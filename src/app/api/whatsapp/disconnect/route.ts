import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Check if session exists
    const session = whatsappService.getSession(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Destroy the session
    await whatsappService.destroySession(sessionId);

    return NextResponse.json({
      success: true,
      message: 'WhatsApp session disconnected successfully',
      sessionId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error disconnecting WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to disconnect WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Check if session exists
    const whatsappSession = whatsappService.getSession(sessionId);
    if (!whatsappSession) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Destroy the session
    await whatsappService.destroySession(sessionId);

    return NextResponse.json({
      success: true,
      message: 'WhatsApp session deleted successfully',
      sessionId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting WhatsApp session:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to delete WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
