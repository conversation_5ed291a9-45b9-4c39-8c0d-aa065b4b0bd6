import { NextRequest, NextResponse } from 'next/server';
import { whatsappAssistantHandler } from '@/lib/whatsapp/assistant-handler';
import { WhatsAppSessionDatabase } from '@/lib/database/whatsapp-sessions';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';
import {
  validateInternalApiCall,
  getUserFromToken,
} from '@/lib/auth/internal-api';

export async function POST(request: NextRequest) {
  try {
    // Check if this is an internal API call (server-to-server)
    const isInternalCall = request.headers.get('X-Internal-API') === 'true';

    if (isInternalCall) {
      // Validate internal API call authentication using JW<PERSON> token
      const token = validateInternalApiCall(request);
      if (!token) {
        return NextResponse.json(
          { error: 'Unauthorized - Invalid internal API authentication' },
          { status: 401 }
        );
      }

      const user = getUserFromToken(token);
      console.log(
        `[WhatsApp Process Message API] Internal API call from user: ${user.email}`
      );
    } else {
      // Regular client call - middleware already handled authentication
      console.log(
        `[WhatsApp Process Message API] Client API call (authenticated by middleware)`
      );
    }

    const body = await request.json();
    const { sessionId, message } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Validate message format
    if (!message.id || !message.from || !message.message || !message.type) {
      return NextResponse.json(
        {
          error:
            'Invalid message format. Required fields: id, from, message, type',
        },
        { status: 400 }
      );
    }

    console.log(`[API] Processing message for session: ${sessionId}`);
    console.log(`[API] Message:`, message);

    // Get session from database to check for assistant configuration
    const session = await WhatsAppSessionDatabase.getSession(sessionId);
    if (!session) {
      console.log(`[API] Session not found in database: ${sessionId}`);
      return NextResponse.json({
        success: true,
        message: 'Session not found in database',
        sessionId,
        processed: false,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if session has an assistant configuration
    if (!session.assistant_config_id) {
      console.log(
        `[API] No assistant configuration linked to session: ${sessionId}`
      );
      return NextResponse.json({
        success: true,
        message: 'No assistant configuration linked to session',
        sessionId,
        processed: false,
        timestamp: new Date().toISOString(),
      });
    }

    // Get assistant configuration from database
    const assistantConfig =
      await AssistantConfigurationDatabase.getConfiguration(
        session.assistant_config_id
      );
    if (!assistantConfig) {
      console.log(
        `[API] Assistant configuration not found: ${session.assistant_config_id}`
      );
      return NextResponse.json({
        success: true,
        message: 'Assistant configuration not found',
        sessionId,
        processed: false,
        timestamp: new Date().toISOString(),
      });
    }

    console.log(
      `[API] Found assistant configuration for session: ${sessionId}`
    );

    // Enable assistant with the configuration from database
    whatsappAssistantHandler.enableAssistant(sessionId, {
      salesAssistant: assistantConfig.sales_assistant,
      businessSetup: assistantConfig.business_setup,
      productKnowledge: assistantConfig.product_knowledge,
    });

    console.log(`[API] Processing message with assistant...`);
    // Process the message with assistant
    await whatsappAssistantHandler.processIncomingMessage(sessionId, message);

    return NextResponse.json({
      success: true,
      message: 'Message processed successfully',
      sessionId,
      processed: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error processing WhatsApp message:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to process WhatsApp message',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
