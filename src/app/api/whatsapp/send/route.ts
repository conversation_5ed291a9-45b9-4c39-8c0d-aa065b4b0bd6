import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';
import {
  validateInternalApiCall,
  getUserFromToken,
} from '@/lib/auth/internal-api';

export async function POST(request: NextRequest) {
  try {
    // Check if this is an internal API call (server-to-server)
    const isInternalCall = request.headers.get('X-Internal-API') === 'true';

    if (isInternalCall) {
      // Validate internal API call authentication using JWT token
      const token = validateInternalApiCall(request);
      if (!token) {
        return NextResponse.json(
          { error: 'Unauthorized - Invalid internal API authentication' },
          { status: 401 }
        );
      }

      const user = getUserFromToken(token);
      console.log(
        `[WhatsApp Send API] Internal API call from user: ${user.email}`
      );
    } else {
      // Regular client call - middleware already handled authentication
      console.log(
        `[WhatsApp Send API] Client API call (authenticated by middleware)`
      );
    }

    const body = await request.json();
    const { sessionId, to, message } = body;

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    if (!to) {
      return NextResponse.json(
        { error: 'Recipient phone number is required' },
        { status: 400 }
      );
    }

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if session exists and is connected
    const session = whatsappService.getSession(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    if (session.status !== 'connected') {
      return NextResponse.json(
        { error: `Session not connected. Current status: ${session.status}` },
        { status: 400 }
      );
    }

    // Format phone number (ensure it includes country code and @s.whatsapp.net)
    let formattedTo = to.replace(/[^\d]/g, ''); // Remove non-digits
    if (!formattedTo.startsWith('1') && formattedTo.length === 10) {
      // Assume US number if 10 digits
      formattedTo = '1' + formattedTo;
    }
    formattedTo = formattedTo + '@s.whatsapp.net';

    // Send message
    const sentMessage = await whatsappService.sendMessage(
      sessionId,
      formattedTo,
      message
    );

    return NextResponse.json({
      success: true,
      message: sentMessage,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send WhatsApp message',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
