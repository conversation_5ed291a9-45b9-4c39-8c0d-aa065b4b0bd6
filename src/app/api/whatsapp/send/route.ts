import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, to, message } = body;

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    if (!to) {
      return NextResponse.json(
        { error: 'Recipient phone number is required' },
        { status: 400 }
      );
    }

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if session exists and is connected
    const session = whatsappService.getSession(sessionId);
    if (!session) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    if (session.status !== 'connected') {
      return NextResponse.json(
        { error: `Session not connected. Current status: ${session.status}` },
        { status: 400 }
      );
    }

    // Format phone number (ensure it includes country code and @s.whatsapp.net)
    let formattedTo = to.replace(/[^\d]/g, ''); // Remove non-digits
    if (!formattedTo.startsWith('1') && formattedTo.length === 10) {
      // Assume US number if 10 digits
      formattedTo = '1' + formattedTo;
    }
    formattedTo = formattedTo + '@s.whatsapp.net';

    // Send message
    const sentMessage = await whatsappService.sendMessage(
      sessionId,
      formattedTo,
      message
    );

    return NextResponse.json({
      success: true,
      message: sentMessage,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send WhatsApp message',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
