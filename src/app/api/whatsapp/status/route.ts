import { NextRequest, NextResponse } from 'next/server';
import { whatsappService } from '@/lib/whatsapp/service';
import { webSocketManager } from '@/lib/websocket/manager';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (sessionId) {
      // Get specific session status
      const session = whatsappService.getSession(sessionId);

      if (!session) {
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        session: {
          id: session.id,
          status: session.status,
          qrCode: session.qrCode,
          phoneNumber: session.phoneNumber,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
        },
        websocket: {
          subscribers: webSocketManager.getSessionSubscribers(sessionId),
        },
      });
    } else {
      // Get all sessions status
      const sessions = whatsappService.getAllSessions();

      return NextResponse.json({
        success: true,
        sessions: sessions.map(session => ({
          id: session.id,
          status: session.status,
          qrCode: session.qrCode,
          phoneNumber: session.phoneNumber,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
        })),
        websocket: {
          totalClients: webSocketManager.getConnectedClients(),
        },
        totalSessions: sessions.length,
      });
    }
  } catch (error) {
    console.error('Error getting WhatsApp status:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to get WhatsApp status',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
