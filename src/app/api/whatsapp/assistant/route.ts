import { NextRequest, NextResponse } from 'next/server';
import { whatsappAssistantHandler } from '@/lib/whatsapp/assistant-handler';
import { AssistantConfigurationDatabase } from '@/lib/database/assistant-configurations';

// Enable assistant for a WhatsApp session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, assistantConfig, assistantConfigId } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    let finalAssistantConfig = assistantConfig;

    // If assistantConfigId is provided, fetch the configuration from database
    if (assistantConfigId) {
      console.log(
        `[API] Fetching assistant config from database: ${assistantConfigId}`
      );
      const configFromDb =
        await AssistantConfigurationDatabase.getConfiguration(
          assistantConfigId
        );

      if (!configFromDb) {
        return NextResponse.json(
          { error: 'Assistant configuration not found' },
          { status: 404 }
        );
      }

      // Convert database format to the format expected by the handler
      finalAssistantConfig = {
        businessSetup: configFromDb.business_setup,
        salesAssistant: configFromDb.sales_assistant,
        productKnowledge: configFromDb.product_knowledge,
      };
    } else if (!assistantConfig) {
      return NextResponse.json(
        { error: 'Either assistantConfig or assistantConfigId is required' },
        { status: 400 }
      );
    }

    console.log(`[API] Enabling assistant for session: ${sessionId}`);
    console.log(`[API] Assistant config:`, finalAssistantConfig);

    // Enable assistant for the session
    whatsappAssistantHandler.enableAssistant(sessionId, finalAssistantConfig);

    return NextResponse.json({
      success: true,
      message: 'Assistant enabled for WhatsApp session',
      sessionId,
      assistantConfigId: assistantConfigId ?? null,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error enabling WhatsApp assistant:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to enable WhatsApp assistant',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Disable assistant for a WhatsApp session
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Disable assistant for the session
    whatsappAssistantHandler.disableAssistant(sessionId);

    return NextResponse.json({
      success: true,
      message: 'Assistant disabled for WhatsApp session',
      sessionId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error disabling WhatsApp assistant:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to disable WhatsApp assistant',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Get assistant status for a session
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const isEnabled = whatsappAssistantHandler.isAssistantEnabled(sessionId);
    const config = whatsappAssistantHandler.getAssistantConfig(sessionId);

    return NextResponse.json({
      sessionId,
      assistantEnabled: isEnabled,
      assistantConfig: config,
      activeSessions: whatsappAssistantHandler.getActiveSessions(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting WhatsApp assistant status:', error);

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'Failed to get WhatsApp assistant status',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
