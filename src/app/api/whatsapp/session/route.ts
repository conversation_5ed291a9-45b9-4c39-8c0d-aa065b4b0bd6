import { NextRequest, NextResponse } from 'next/server';
import { WhatsAppSessionDatabase } from '@/lib/database/whatsapp-sessions';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Update WhatsApp session
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { sessionId, assistantConfigId, userId } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Verify user owns this session
    if (userId && userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Update the session with assistant configuration ID
    const updatedSession = await WhatsAppSessionDatabase.updateSession(sessionId, {
      assistant_config_id: assistantConfigId,
      user_id: session.user.id,
    });

    if (!updatedSession) {
      return NextResponse.json(
        { error: 'Failed to update WhatsApp session' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      session: updatedSession,
      message: 'WhatsApp session updated successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating WhatsApp session:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to update WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Get WhatsApp session
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const whatsappSession = await WhatsAppSessionDatabase.getSession(sessionId);

    if (!whatsappSession) {
      return NextResponse.json(
        { error: 'WhatsApp session not found' },
        { status: 404 }
      );
    }

    // Check if user owns this session
    if (whatsappSession.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      session: whatsappSession,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting WhatsApp session:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to get WhatsApp session',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
