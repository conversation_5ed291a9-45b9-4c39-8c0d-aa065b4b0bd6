'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import UserDropdown from '@/components/ui/UserDropdown';

interface AssistantConfiguration {
  id: string;
  name: string;
  description?: string;
  business_setup: any;
  sales_assistant: any;
  product_knowledge: any;
  status: 'draft' | 'configured' | 'active';
  created_at: string;
  updated_at: string;
}

export default function AssistantsPage() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();
  const [assistants, setAssistants] = useState<AssistantConfiguration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'configured' | 'active'>('all');
  const [deleteConfirm, setDeleteConfirm] = useState<{ show: boolean; assistant: AssistantConfiguration | null }>({
    show: false,
    assistant: null,
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Fetch assistants
  useEffect(() => {
    if (isAuthenticated) {
      fetchAssistants();
    }
  }, [isAuthenticated, searchTerm, statusFilter]);



  const fetchAssistants = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/assistant?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        setAssistants(data.configurations || []);
      } else {
        setError(data.error || 'Failed to fetch assistants');
      }
    } catch (error) {
      console.error('Error fetching assistants:', error);
      setError('Failed to fetch assistants');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (assistant: AssistantConfiguration) => {
    try {
      const response = await fetch(`/api/assistant/${assistant.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setAssistants(prev => prev.filter(a => a.id !== assistant.id));
        setDeleteConfirm({ show: false, assistant: null });
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to delete assistant');
      }
    } catch (error) {
      console.error('Error deleting assistant:', error);
      setError('Failed to delete assistant');
    }
  };

  const handleClone = async (assistant: AssistantConfiguration) => {
    try {
      const response = await fetch(`/api/assistant/${assistant.id}/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `${assistant.name} (Copy)`,
        }),
      });

      if (response.ok) {
        fetchAssistants(); // Refresh the list
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to clone assistant');
      }
    } catch (error) {
      console.error('Error cloning assistant:', error);
      setError('Failed to clone assistant');
    }
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'configured': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return '📝';
      case 'configured': return '⚙️';
      case 'active': return '🟢';
      default: return '❓';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <nav className="container mx-auto px-4 py-6">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-gray-900 dark:text-white">
            SalesFlow AI
          </Link>
          <div className="flex items-center gap-4">
            <Link
              href="/builder"
              className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              Builder
            </Link>

            {/* User Menu */}
            <UserDropdown user={user} />
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                My Assistants
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Manage your AI sales assistants
              </p>
            </div>
            <Link
              href="/builder"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              + Create New Assistant
            </Link>
          </div>

          {/* Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search assistants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="configured">Configured</option>
                  <option value="active">Active</option>
                </select>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {/* Assistants Grid */}
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">Loading assistants...</p>
            </div>
          ) : assistants.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🤖</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No assistants found
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Get started by creating your first AI sales assistant.'
                }
              </p>
              <Link
                href="/builder"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
              >
                Create Your First Assistant
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {assistants.map((assistant) => (
                <div
                  key={assistant.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
                >
                  {/* Status Badge */}
                  <div className="flex justify-between items-start mb-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(assistant.status)}`}>
                      {getStatusIcon(assistant.status)} {assistant.status}
                    </span>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleClone(assistant)}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        title="Clone assistant"
                      >
                        📋
                      </button>
                      <button
                        onClick={() => setDeleteConfirm({ show: true, assistant })}
                        className="text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                        title="Delete assistant"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  {/* Assistant Info */}
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {assistant.name}
                  </h3>
                  {assistant.description && (
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                      {assistant.description}
                    </p>
                  )}

                  {/* Business Info */}
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <p>Company: {assistant.business_setup?.companyName || 'Not set'}</p>
                    <p>Industry: {assistant.business_setup?.industry || 'Not set'}</p>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Link
                      href={`/builder?edit=${assistant.id}`}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-3 rounded text-sm transition-colors"
                    >
                      Edit
                    </Link>
                    {assistant.status === 'configured' && (
                      <Link
                        href={`/preview?assistant=${assistant.id}`}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-3 rounded text-sm transition-colors"
                      >
                        Test
                      </Link>
                    )}
                  </div>

                  {/* Timestamps */}
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
                    <p>Created: {new Date(assistant.created_at).toLocaleDateString()}</p>
                    <p>Updated: {new Date(assistant.updated_at).toLocaleDateString()}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirm.show && deleteConfirm.assistant && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Delete Assistant
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Are you sure you want to delete "{deleteConfirm.assistant.name}"? This action cannot be undone.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setDeleteConfirm({ show: false, assistant: null })}
                className="flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDelete(deleteConfirm.assistant!)}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
