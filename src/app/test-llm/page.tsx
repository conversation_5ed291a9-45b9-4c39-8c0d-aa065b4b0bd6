'use client';

import { useState } from 'react';

interface TestResult {
  status?: string;
  message?: string;
  response?: string;
  model?: string;
  error?: string;
  usage?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  config?: Record<string, unknown>;
  templates?: Record<string, unknown>[];
  models?: Record<string, unknown>;
}

export default function TestLLM() {
  const [llmResult, setLlmResult] = useState<TestResult | null>(null);
  const [embeddingsResult, setEmbeddingsResult] = useState<TestResult | null>(null);
  const [configResult, setConfigResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [chatMessage, setChatMessage] = useState('Hello! Can you tell me about your products?');

  const setLoadingState = (key: string, value: boolean) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  };

  const testLLMConnection = async () => {
    setLoadingState('llm', true);
    try {
      const response = await fetch('/api/llm', {
        method: 'GET',
      });
      const data = await response.json();
      setLlmResult(data);
    } catch {
      setLlmResult({ error: 'Failed to connect to LLM API' });
    }
    setLoadingState('llm', false);
  };

  const testLLMChat = async () => {
    setLoadingState('chat', true);
    try {
      const response = await fetch('/api/llm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: chatMessage,
          assistantConfig: {
            name: 'TestBot',
            personality: 'friendly',
            tone: 'professional',
            objectives: ['Help customers find the right products', 'Provide excellent service'],
          },
          businessInfo: {
            companyName: 'SalesFlow AI',
            industry: 'Technology',
            description: 'AI-powered sales assistant platform',
          },
          products: [
            {
              name: 'SalesFlow Pro',
              description: 'Advanced AI sales assistant with WhatsApp integration',
              price: 99,
              category: 'Software',
            },
            {
              name: 'SalesFlow Enterprise',
              description: 'Enterprise-grade sales automation platform',
              price: 299,
              category: 'Software',
            },
          ],
          userId: 'test-user-123',
        }),
      });

      const data = await response.json();
      setLlmResult(data);
    } catch {
      setLlmResult({ error: 'Failed to get LLM response' });
    }
    setLoadingState('chat', false);
  };

  const testEmbeddings = async () => {
    setLoadingState('embeddings', true);
    try {
      const response = await fetch('/api/embeddings', {
        method: 'GET',
      });
      const data = await response.json();
      setEmbeddingsResult(data);
    } catch {
      setEmbeddingsResult({ error: 'Failed to connect to Embeddings API' });
    }
    setLoadingState('embeddings', false);
  };

  const testProductEmbeddings = async () => {
    setLoadingState('productEmbeddings', true);
    try {
      const response = await fetch('/api/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate_product_embeddings',
          userId: 'test-user-123',
          data: {
            products: [
              {
                name: 'AI Sales Assistant',
                description: 'Intelligent sales assistant powered by GPT-4 for customer engagement',
                price: 99,
                category: 'Software',
                metadata: { features: ['WhatsApp integration', 'Real-time responses', 'Analytics'] },
              },
              {
                name: 'Customer Support Bot',
                description: 'Automated customer support with natural language processing',
                price: 149,
                category: 'Software',
                metadata: { features: ['24/7 availability', 'Multi-language', 'Ticket routing'] },
              },
            ],
          },
        }),
      });

      const data = await response.json();
      setEmbeddingsResult(data);
    } catch {
      setEmbeddingsResult({ error: 'Failed to generate product embeddings' });
    }
    setLoadingState('productEmbeddings', false);
  };

  const testConfiguration = async () => {
    setLoadingState('config', true);
    try {
      const response = await fetch('/api/config', {
        method: 'GET',
      });
      const data = await response.json();
      setConfigResult(data);
    } catch {
      setConfigResult({ error: 'Failed to fetch configuration' });
    }
    setLoadingState('config', false);
  };

  const testTemplates = async () => {
    setLoadingState('templates', true);
    try {
      const response = await fetch('/api/config?action=templates&category=sales', {
        method: 'GET',
      });
      const data = await response.json();
      setConfigResult(data);
    } catch {
      setConfigResult({ error: 'Failed to fetch templates' });
    }
    setLoadingState('templates', false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            OpenAI LLM API Testing
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Test all OpenAI integration features including LLM, embeddings, and configuration
          </p>
        </div>

        {/* LLM Testing */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            1. LLM API Testing
          </h2>

          <div className="space-y-4">
            <button
              onClick={testLLMConnection}
              disabled={loading.llm}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors mr-4"
            >
              {loading.llm ? 'Testing...' : 'Test LLM Connection'}
            </button>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Test Message:
              </label>
              <textarea
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                rows={3}
                placeholder="Enter your test message..."
              />
              <button
                onClick={testLLMChat}
                disabled={loading.chat || !chatMessage.trim()}
                className="mt-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {loading.chat ? 'Getting Response...' : 'Test LLM Chat'}
              </button>
            </div>

            {llmResult && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">LLM Result:</h3>
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-auto max-h-96">
                  {JSON.stringify(llmResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Embeddings Testing */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            2. Embeddings API Testing
          </h2>

          <div className="space-y-4">
            <button
              onClick={testEmbeddings}
              disabled={loading.embeddings}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg transition-colors mr-4"
            >
              {loading.embeddings ? 'Testing...' : 'Test Embeddings Connection'}
            </button>

            <button
              onClick={testProductEmbeddings}
              disabled={loading.productEmbeddings}
              className="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
            >
              {loading.productEmbeddings ? 'Generating...' : 'Generate Product Embeddings'}
            </button>

            {embeddingsResult && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">Embeddings Result:</h3>
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-auto max-h-96">
                  {JSON.stringify(embeddingsResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Configuration Testing */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            3. Configuration API Testing
          </h2>

          <div className="space-y-4">
            <button
              onClick={testConfiguration}
              disabled={loading.config}
              className="px-6 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white rounded-lg transition-colors mr-4"
            >
              {loading.config ? 'Loading...' : 'Test Configuration API'}
            </button>

            <button
              onClick={testTemplates}
              disabled={loading.templates}
              className="px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
            >
              {loading.templates ? 'Loading...' : 'Test Prompt Templates'}
            </button>

            {configResult && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">Configuration Result:</h3>
                <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-auto max-h-96">
                  {JSON.stringify(configResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
            Testing Instructions
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 dark:text-blue-200">
            <li><strong>LLM API</strong>: Test connection and chat completion with rate limiting and error handling</li>
            <li><strong>Embeddings API</strong>: Test embeddings generation and product data processing</li>
            <li><strong>Configuration API</strong>: Test model selection, prompt templates, and user configurations</li>
            <li>Check the results for proper error handling, token usage tracking, and response formatting</li>
            <li>Verify that all features work correctly with the enhanced OpenAI integration</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
