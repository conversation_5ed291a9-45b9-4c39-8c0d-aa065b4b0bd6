'use client';

import { useState, useEffect, useRef } from 'react';

interface WhatsAppSession {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'qr_required';
  qrCode?: string;
  phoneNumber?: string;
  createdAt: string;
  lastActivity: string;
}

interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  message: string;
  timestamp: string;
  type: 'incoming' | 'outgoing';
}

interface AssistantConfig {
  salesAssistant: {
    name: string;
    personality: string;
    tone: string;
    objectives: string[];
  };
  businessSetup: {
    companyName: string;
    industry: string;
    description: string;
  };
  productKnowledge: {
    products: Array<{
      name: string;
      description: string;
      price: number;
      category: string;
    }>;
  };
}

export default function TestWhatsAppAssistantPage() {
  const [session, setSession] = useState<WhatsAppSession | null>(null);
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [phoneInput, setPhoneInput] = useState('');
  const [assistantEnabled, setAssistantEnabled] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  const sessionId = 'assistant-test-session-' + Date.now();

  // Sample assistant configuration
  const assistantConfig: AssistantConfig = {
    salesAssistant: {
      name: 'SalesBot',
      personality: 'friendly and professional',
      tone: 'conversational',
      objectives: ['Help customers find products', 'Answer questions', 'Generate leads'],
    },
    businessSetup: {
      companyName: 'Test Company',
      industry: 'Technology',
      description: 'A test company for WhatsApp assistant integration',
    },
    productKnowledge: {
      products: [
        {
          name: 'Premium Software',
          description: 'Advanced business software solution',
          price: 299,
          category: 'Software',
        },
        {
          name: 'Basic Plan',
          description: 'Starter package for small businesses',
          price: 99,
          category: 'Service',
        },
      ],
    },
  };

  useEffect(() => {
    return () => {
      // Cleanup WebSocket on unmount
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const connectWebSocket = (sessionId: string) => {
    try {
      const ws = new WebSocket('ws://localhost:3001');
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        // Subscribe to session events
        ws.send(JSON.stringify({
          type: 'subscribe',
          sessionId: sessionId
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('WebSocket message:', data);

          switch (data.type) {
            case 'qr_code':
              setSession(prev => prev ? { ...prev, qrCode: data.data.qrCode, status: 'qr_required' } : null);
              break;
            case 'connected':
              setSession(prev => prev ? {
                ...prev,
                status: 'connected',
                phoneNumber: data.data.phoneNumber,
                qrCode: undefined
              } : null);
              break;
            case 'disconnected':
              setSession(prev => prev ? { ...prev, status: 'disconnected', qrCode: undefined } : null);
              break;
            case 'restart_required':
              setTimeout(() => {
                connectWebSocket(sessionId);
              }, 2000);
              break;
            case 'message_received': {
              const incomingMessage = data.data;
              setMessages(prev => [...prev, incomingMessage]);

              // Always try to process with assistant - the API will check if it's enabled
              console.log('Received message, processing with assistant:', incomingMessage);
              processMessageWithAssistant(sessionId, incomingMessage);
              break;
            }
            case 'message_sent':
              setMessages(prev => [...prev, data.data]);
              break;
            case 'connection_status':
              console.log('Connection status:', data.data);
              if (data.data.qrCode) {
                setSession(prev => prev ? { ...prev, qrCode: data.data.qrCode, status: 'qr_required' } : null);
              }
              break;
            case 'error':
              console.error('WebSocket error:', data.data);
              setError(data.data.error);
              break;
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('WebSocket connection error');
      };
    } catch (error) {
      console.error('Error connecting WebSocket:', error);
      setError('Failed to connect to WebSocket');
    }
  };

  const processMessageWithAssistant = async (sessionId: string, message: WhatsAppMessage) => {
    try {
      console.log('Processing message with assistant:', message);
      const response = await fetch('/api/whatsapp/process-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          message,
        }),
      });

      const result = await response.json();
      console.log('Assistant processing result:', result);

      if (!response.ok) {
        console.error('Failed to process message with assistant:', result.error);
      }
    } catch (error) {
      console.error('Error processing message with assistant:', error);
    }
  };

  const handleConnect = async () => {
    setLoading(true);
    setError(null);

    try {
      // Connect WebSocket first
      connectWebSocket(sessionId);

      // Create WhatsApp session
      const response = await fetch('/api/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      const data = await response.json();

      if (response.ok) {
        setSession(data.session);
      } else {
        setError(data.error || 'Failed to connect');
      }
    } catch (error) {
      console.error('Error connecting:', error);
      setError('Failed to connect to WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      // Disable assistant first
      if (assistantEnabled) {
        await handleToggleAssistant();
      }

      const response = await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId: session.id }),
      });

      const data = await response.json();

      if (response.ok) {
        setSession(null);
        setMessages([]);
        if (wsRef.current) {
          wsRef.current.close();
        }
      } else {
        setError(data.error || 'Failed to disconnect');
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
      setError('Failed to disconnect from WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAssistant = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      if (assistantEnabled) {
        // Disable assistant
        const response = await fetch(`/api/whatsapp/assistant?sessionId=${session.id}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          setAssistantEnabled(false);
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to disable assistant');
        }
      } else {
        // Enable assistant
        const response = await fetch('/api/whatsapp/assistant', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sessionId: session.id,
            assistantConfig,
          }),
        });

        if (response.ok) {
          setAssistantEnabled(true);
        } else {
          const data = await response.json();
          setError(data.error || 'Failed to enable assistant');
        }
      }
    } catch (error) {
      console.error('Error toggling assistant:', error);
      setError('Failed to toggle assistant');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!session || !messageInput.trim() || !phoneInput.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/whatsapp/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: session.id,
          to: phoneInput,
          message: messageInput,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessageInput('');
      } else {
        setError(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            WhatsApp Assistant Integration Test
          </h1>

          {error && (
            <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Connection Panel */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Connection
              </h2>

              {!session ? (
                <button
                  onClick={handleConnect}
                  disabled={loading}
                  className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                >
                  {loading ? 'Connecting...' : 'Connect WhatsApp'}
                </button>
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Status:</strong> {session.status}
                    </p>
                    {session.phoneNumber && (
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        <strong>Phone:</strong> {session.phoneNumber}
                      </p>
                    )}
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong>Assistant:</strong> {assistantEnabled ? 'Enabled' : 'Disabled'}
                    </p>
                  </div>

                  {session.qrCode && (
                    <div className="text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        Scan this QR code with WhatsApp:
                      </p>
                      <img
                        src={session.qrCode}
                        alt="WhatsApp QR Code"
                        className="mx-auto border rounded-lg max-w-full"
                      />
                    </div>
                  )}

                  {session.status === 'connected' && (
                    <button
                      onClick={handleToggleAssistant}
                      disabled={loading}
                      className={`w-full px-4 py-2 ${
                        assistantEnabled
                          ? 'bg-orange-600 hover:bg-orange-700'
                          : 'bg-blue-600 hover:bg-blue-700'
                      } disabled:bg-gray-400 text-white rounded-lg transition-colors`}
                    >
                      {loading ? 'Processing...' : assistantEnabled ? 'Disable Assistant' : 'Enable Assistant'}
                    </button>
                  )}

                  <button
                    onClick={handleDisconnect}
                    disabled={loading}
                    className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                  >
                    {loading ? 'Disconnecting...' : 'Disconnect'}
                  </button>
                </div>
              )}
            </div>

            {/* Assistant Config Panel */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Assistant Configuration
              </h2>

              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm">
                <div className="space-y-2">
                  <p><strong>Name:</strong> {assistantConfig.salesAssistant.name}</p>
                  <p><strong>Company:</strong> {assistantConfig.businessSetup.companyName}</p>
                  <p><strong>Industry:</strong> {assistantConfig.businessSetup.industry}</p>
                  <p><strong>Products:</strong> {assistantConfig.productKnowledge.products.length} items</p>
                  <p><strong>Personality:</strong> {assistantConfig.salesAssistant.personality}</p>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">How it works:</h3>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Connect WhatsApp and enable assistant</li>
                  <li>• Send messages to your WhatsApp number</li>
                  <li>• Assistant will automatically respond</li>
                  <li>• View conversation history below</li>
                </ul>
              </div>
            </div>

            {/* Messaging Panel */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Messages
              </h2>

              {session?.status === 'connected' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="Phone number (e.g., +1234567890)"
                      value={phoneInput}
                      onChange={(e) => setPhoneInput(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <textarea
                      placeholder="Type your message..."
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <button
                      onClick={handleSendMessage}
                      disabled={loading || !messageInput.trim() || !phoneInput.trim()}
                      className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                    >
                      {loading ? 'Sending...' : 'Send Message'}
                    </button>
                  </div>
                </div>
              )}

              {/* Message History */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      msg.type === 'outgoing'
                        ? 'bg-blue-50 dark:bg-blue-900/20 ml-4'
                        : 'bg-gray-50 dark:bg-gray-700 mr-4'
                    }`}
                  >
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong>{msg.type === 'outgoing' ? 'You' : msg.from}:</strong>
                    </p>
                    <p className="text-gray-900 dark:text-white">{msg.message}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
