/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',

  // External packages for server components
  serverExternalPackages: [
    '@whiskeysockets/baileys',
    'ws',
    'bufferutil',
    'utf-8-validate',
  ],

  // Disable ESLint during build for Docker
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },

  // Disable TypeScript checking during build for Docker
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // Image optimization
  images: {
    domains: ['lh3.googleusercontent.com'], // For Google profile images
    unoptimized: false,
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },

  // Environment variables that should be available in the browser
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // Webpack configuration for better bundling
  webpack: (config, { isServer }) => {
    if (isServer) {
      // External packages that should not be bundled on server
      config.externals.push(
        '@whiskeysockets/baileys',
        'ws',
        'bufferutil',
        'utf-8-validate'
      );

      // Fix for WebSocket masking issues in Docker
      config.resolve.fallback = {
        ...config.resolve.fallback,
        bufferutil: false,
        'utf-8-validate': false,
      };
    } else {
      // Optimize bundle size for client
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        bufferutil: false,
        'utf-8-validate': false,
      };
    }

    return config;
  },

  // Disable telemetry in production (deprecated in Next.js 15)
  // telemetry: false,
};

module.exports = nextConfig;
