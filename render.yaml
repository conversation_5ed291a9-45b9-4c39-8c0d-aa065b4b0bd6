# Render deployment configuration for SalesFlow AI
# https://render.com/docs/blueprint-spec

services:
  - type: web
    name: salesflow-ai
    env: docker
    dockerfilePath: ./Dockerfile
    dockerContext: .
    
    # Auto-deploy from Git
    autoDeploy: true
    branch: main
    
    # Resource allocation
    plan: starter # Can be upgraded to standard/pro as needed
    
    # Health check configuration
    healthCheckPath: /api/health
    
    # Environment variables
    envVars:
      - key: NODE_ENV
        value: production
      
      - key: NEXTAUTH_URL
        sync: false # Set manually in Render dashboard
        
      - key: NEXTAUTH_SECRET
        sync: false # Set manually in Render dashboard
        
      - key: GOOGLE_CLIENT_ID
        sync: false # Set manually in Render dashboard
        
      - key: GOOGLE_CLIENT_SECRET
        sync: false # Set manually in Render dashboard
        
      - key: ALLOWED_EMAILS
        sync: false # Set manually in Render dashboard
        
      - key: NEXT_PUBLIC_SUPABASE_URL
        sync: false # Set manually in Render dashboard
        
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        sync: false # Set manually in Render dashboard
        
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false # Set manually in Render dashboard
        
      - key: OPENAI_API_KEY
        sync: false # Set manually in Render dashboard
        
      - key: WHATSAPP_SESSION_PATH
        value: /app/whatsapp-sessions
        
      - key: PORT
        value: 3000
        
      - key: HOSTNAME
        value: 0.0.0.0
    
    # Disk storage for WhatsApp sessions
    disk:
      name: whatsapp-sessions
      mountPath: /app/whatsapp-sessions
      sizeGB: 1
    
    # Build configuration
    buildCommand: echo "Using Docker build"
    startCommand: echo "Using Docker CMD"
    
    # Scaling configuration
    numInstances: 1
    
    # Custom domains (configure in Render dashboard)
    # domains:
    #   - salesflow-ai.yourdomain.com

# Optional: Database service (if using managed PostgreSQL)
# databases:
#   - name: salesflow-ai-db
#     databaseName: salesflow_ai
#     user: salesflow_ai_user
#     plan: starter

# Optional: Redis service (for caching/sessions)
# - type: redis
#   name: salesflow-ai-redis
#   plan: starter
