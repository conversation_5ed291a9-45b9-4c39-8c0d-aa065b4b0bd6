-- Update assistant_configurations table to match the application schema
-- This migration adds the missing columns that the application expects

-- Add the missing columns to assistant_configurations table
ALTER TABLE assistant_configurations 
ADD COLUMN IF NOT EXISTS business_setup JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS sales_assistant J<PERSON><PERSON><PERSON> DEFAULT '{}',
ADD COLUMN IF NOT EXISTS product_knowledge JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'draft';

-- Add check constraint for status values
ALTER TABLE assistant_configurations 
ADD CONSTRAINT assistant_configurations_status_check 
CHECK (status IN ('draft', 'configured', 'active'));

-- Create index for status column for better performance
CREATE INDEX IF NOT EXISTS idx_assistant_configurations_status 
ON assistant_configurations(status);

-- Update existing records to have default values if they don't already
UPDATE assistant_configurations 
SET 
    business_setup = COALESCE(business_setup, '{}'),
    sales_assistant = COALESCE(sales_assistant, '{}'),
    product_knowledge = COALESCE(product_knowledge, '{}'),
    status = COALESCE(status, 'draft')
WHERE 
    business_setup IS NULL 
    OR sales_assistant IS NULL 
    OR product_knowledge IS NULL 
    OR status IS NULL;
