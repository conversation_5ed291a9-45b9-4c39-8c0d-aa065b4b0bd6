-- Remove UNIQUE constraint on phone_number column in whatsapp_sessions table
-- This allows multiple WhatsApp sessions to use the same phone number

-- Drop the unique constraint on phone_number if it exists
ALTER TABLE whatsapp_sessions DROP CONSTRAINT IF EXISTS whatsapp_sessions_phone_number_key;

-- Also drop any unique index on phone_number if it exists
DROP INDEX IF EXISTS whatsapp_sessions_phone_number_key;
DROP INDEX IF EXISTS idx_whatsapp_sessions_phone_number_unique;

-- Note: We keep the regular (non-unique) index idx_whatsapp_sessions_phone_number for performance
-- if it exists, as it's useful for queries filtering by phone number
