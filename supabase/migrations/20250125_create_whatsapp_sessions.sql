-- Create assistant configurations table (independent entity)
CREATE TABLE IF NOT EXISTS assistant_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL, -- Changed from UUID to VA<PERSON>HAR for OAuth compatibility
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    business_setup JSONB NOT NULL,
    sales_assistant JSONB NOT NULL,
    product_knowledge JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'configured', 'active')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create WhatsApp sessions table for persistent session storage
CREATE TABLE IF NOT EXISTS whatsapp_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    phone_number VARCHAR(50), -- Allow multiple sessions per phone number
    user_id VARCHAR(255), -- Changed from UUID to <PERSON><PERSON><PERSON><PERSON> for OAuth compatibility
    assistant_config_id UUID REFERENCES assistant_configurations(id) ON DELETE SET NULL, -- Reference to assistant config
    status VARCHAR(50) DEFAULT 'disconnected' CHECK (status IN ('disconnected', 'connecting', 'connected', 'qr_required')),
    qr_code TEXT, -- Store QR code temporarily
    connection_data JSONB, -- Store Baileys connection data
    credentials JSONB, -- Store encrypted credentials
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_assistant_configurations_user_id ON assistant_configurations(user_id);
CREATE INDEX IF NOT EXISTS idx_assistant_configurations_status ON assistant_configurations(status);

CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_session_id ON whatsapp_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_phone_number ON whatsapp_sessions(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_user_id ON whatsapp_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_status ON whatsapp_sessions(status);
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_assistant_config_id ON whatsapp_sessions(assistant_config_id);

-- Create WhatsApp messages table for message history
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES whatsapp_sessions(id) ON DELETE CASCADE,
    message_id VARCHAR(255), -- WhatsApp message ID
    from_number VARCHAR(50) NOT NULL,
    to_number VARCHAR(50) NOT NULL,
    message_text TEXT,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'document', 'audio', 'video')),
    direction VARCHAR(20) NOT NULL CHECK (direction IN ('incoming', 'outgoing')),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    processed_by_assistant BOOLEAN DEFAULT FALSE,
    assistant_response_id UUID REFERENCES whatsapp_messages(id), -- Link to assistant response
    metadata JSONB, -- Store additional message metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for message queries
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_session_id ON whatsapp_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_direction ON whatsapp_messages(direction);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_from_number ON whatsapp_messages(from_number);



-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_assistant_configurations_updated_at
    BEFORE UPDATE ON assistant_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_whatsapp_sessions_updated_at
    BEFORE UPDATE ON whatsapp_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE whatsapp_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE assistant_configurations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for whatsapp_sessions (temporarily disabled for OAuth compatibility)
-- Note: These policies need to be updated to work with OAuth user IDs
-- CREATE POLICY "Users can view their own WhatsApp sessions" ON whatsapp_sessions
--     FOR SELECT USING (auth.uid() = user_id);

-- CREATE POLICY "Users can insert their own WhatsApp sessions" ON whatsapp_sessions
--     FOR INSERT WITH CHECK (auth.uid() = user_id);

-- CREATE POLICY "Users can update their own WhatsApp sessions" ON whatsapp_sessions
--     FOR UPDATE USING (auth.uid() = user_id);

-- CREATE POLICY "Users can delete their own WhatsApp sessions" ON whatsapp_sessions
--     FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for whatsapp_messages
CREATE POLICY "Users can view messages from their sessions" ON whatsapp_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM whatsapp_sessions
            WHERE whatsapp_sessions.id = whatsapp_messages.session_id
            AND whatsapp_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages to their sessions" ON whatsapp_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM whatsapp_sessions
            WHERE whatsapp_sessions.id = whatsapp_messages.session_id
            AND whatsapp_sessions.user_id = auth.uid()
        )
    );

-- Create RLS policies for assistant_configurations (temporarily disabled for OAuth compatibility)
-- Note: These policies need to be updated to work with OAuth user IDs
-- CREATE POLICY "Users can view their own assistant configurations" ON assistant_configurations
--     FOR SELECT USING (auth.uid() = user_id);

-- CREATE POLICY "Users can insert their own assistant configurations" ON assistant_configurations
--     FOR INSERT WITH CHECK (auth.uid() = user_id);

-- CREATE POLICY "Users can update their own assistant configurations" ON assistant_configurations
--     FOR UPDATE USING (auth.uid() = user_id);

-- CREATE POLICY "Users can delete their own assistant configurations" ON assistant_configurations
--     FOR DELETE USING (auth.uid() = user_id);
