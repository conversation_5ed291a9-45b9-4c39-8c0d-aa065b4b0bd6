#!/bin/bash

# Render deployment script for SalesFlow AI
# Helps with Render deployment setup and management

set -e

# Configuration
RENDER_SERVICE_NAME="salesflow-ai"
GITHUB_REPO="anchorsprint/sales-flow-ai"

echo "🚀 Render Deployment Helper for SalesFlow AI"
echo "============================================="
echo ""

# Function to show deployment checklist
show_checklist() {
    echo "📋 Pre-deployment Checklist:"
    echo ""
    echo "✅ Required Files:"
    echo "   - Dockerfile (optimized for production)"
    echo "   - render.yaml (deployment configuration)"
    echo "   - next.config.js (with standalone output)"
    echo "   - healthcheck.js (health check script)"
    echo "   - /api/health endpoint"
    echo ""
    echo "🔧 Environment Variables to Set in Render:"
    echo "   - NEXTAUTH_URL (your-app.onrender.com)"
    echo "   - NEXTAUTH_SECRET (generate with: openssl rand -base64 32)"
    echo "   - GOOGLE_CLIENT_ID"
    echo "   - GOOGLE_CLIENT_SECRET"
    echo "   - ALLOWED_EMAILS"
    echo "   - NEXT_PUBLIC_SUPABASE_URL"
    echo "   - NEXT_PUBLIC_SUPABASE_ANON_KEY"
    echo "   - SUPABASE_SERVICE_ROLE_KEY"
    echo "   - OPENAI_API_KEY"
    echo ""
    echo "🌐 Google OAuth Setup:"
    echo "   - Add your Render URL to authorized redirect URIs"
    echo "   - Format: https://your-app.onrender.com/api/auth/callback/google"
    echo ""
}

# Function to validate files
validate_files() {
    echo "🔍 Validating deployment files..."
    
    local files_missing=0
    
    if [ ! -f "Dockerfile" ]; then
        echo "❌ Dockerfile missing"
        files_missing=1
    else
        echo "✅ Dockerfile found"
    fi
    
    if [ ! -f "render.yaml" ]; then
        echo "❌ render.yaml missing"
        files_missing=1
    else
        echo "✅ render.yaml found"
    fi
    
    if [ ! -f "next.config.js" ]; then
        echo "❌ next.config.js missing"
        files_missing=1
    else
        echo "✅ next.config.js found"
        
        # Check for standalone output
        if grep -q "output.*standalone" next.config.js; then
            echo "✅ Standalone output configured"
        else
            echo "⚠️  Standalone output not found in next.config.js"
        fi
    fi
    
    if [ ! -f "healthcheck.js" ]; then
        echo "❌ healthcheck.js missing"
        files_missing=1
    else
        echo "✅ healthcheck.js found"
    fi
    
    if [ ! -f "src/app/api/health/route.ts" ]; then
        echo "❌ Health API endpoint missing"
        files_missing=1
    else
        echo "✅ Health API endpoint found"
    fi
    
    if [ $files_missing -eq 1 ]; then
        echo ""
        echo "❌ Some required files are missing!"
        echo "💡 Run the setup script to create missing files"
        return 1
    else
        echo ""
        echo "✅ All required files present!"
        return 0
    fi
}

# Function to generate NEXTAUTH_SECRET
generate_secret() {
    echo "🔐 Generating NEXTAUTH_SECRET..."
    
    if command -v openssl &> /dev/null; then
        secret=$(openssl rand -base64 32)
        echo "Generated secret: $secret"
        echo ""
        echo "💡 Add this to your Render environment variables:"
        echo "   NEXTAUTH_SECRET=$secret"
    else
        echo "⚠️  OpenSSL not found. Please generate manually:"
        echo "   node -e \"console.log(require('crypto').randomBytes(32).toString('base64'))\""
    fi
    echo ""
}

# Function to show deployment URLs
show_urls() {
    echo "🌐 Important URLs for Render deployment:"
    echo ""
    echo "📊 Render Dashboard:"
    echo "   https://dashboard.render.com/"
    echo ""
    echo "📖 Render Documentation:"
    echo "   https://render.com/docs/docker"
    echo "   https://render.com/docs/environment-variables"
    echo ""
    echo "🔧 GitHub Repository:"
    echo "   https://github.com/$GITHUB_REPO"
    echo ""
}

# Main script logic
case "${1:-help}" in
    "checklist")
        show_checklist
        ;;
    "validate")
        validate_files
        ;;
    "secret")
        generate_secret
        ;;
    "urls")
        show_urls
        ;;
    "help"|*)
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  checklist  - Show pre-deployment checklist"
        echo "  validate   - Validate required files"
        echo "  secret     - Generate NEXTAUTH_SECRET"
        echo "  urls       - Show important URLs"
        echo "  help       - Show this help message"
        echo ""
        echo "💡 Quick start:"
        echo "   1. $0 validate"
        echo "   2. $0 checklist"
        echo "   3. $0 secret"
        echo "   4. Deploy on Render dashboard"
        ;;
esac
