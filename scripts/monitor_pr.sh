#!/bin/bash

# Monitor PR for comments and automatically respond to feedback
# Usage: ./scripts/monitor_pr.sh [PR_NUMBER] [DURATION_MINUTES]

set -e

# Configuration
PR_NUMBER=${1:-32}
DURATION_MINUTES=${2:-60}
CHECK_INTERVAL=30  # seconds
REPO="anchorsprint/sales-flow-ai"

# Calculate total checks
TOTAL_CHECKS=$((DURATION_MINUTES * 60 / CHECK_INTERVAL))

echo "🔍 Starting PR #${PR_NUMBER} monitoring..."
echo "⏰ Duration: ${DURATION_MINUTES} minutes (${TOTAL_CHECKS} checks every ${CHECK_INTERVAL} seconds)"
echo "📝 Watching for comments that need attention..."
echo "📁 Repository: ${REPO}"
echo ""

COUNTER=0
LAST_COMMENT_COUNT=0

# Function to check for comments using GitHub CLI
check_comments() {
    local pr_number=$1

    # Check if gh CLI is available
    if command -v gh &> /dev/null; then
        echo "Using GitHub CLI to check comments..."
        gh pr view $pr_number --repo $REPO --json comments --jq '.comments | length'
    else
        echo "GitHub CLI not available. Manual monitoring required."
        return 1
    fi
}

# Function to get latest comments
get_latest_comments() {
    local pr_number=$1

    if command -v gh &> /dev/null; then
        gh pr view $pr_number --repo $REPO --json comments --jq '.comments[] | {author: .author.login, body: .body, createdAt: .createdAt}'
    fi
}

# Main monitoring loop
while [ $COUNTER -lt $TOTAL_CHECKS ]; do
    COUNTER=$((COUNTER + 1))
    MINUTES_ELAPSED=$((COUNTER * CHECK_INTERVAL / 60))

    # Show progress every 10 checks
    if [ $((COUNTER % 10)) -eq 0 ]; then
        echo "⏱️  ${MINUTES_ELAPSED} minutes elapsed (Check ${COUNTER}/${TOTAL_CHECKS})"
    fi

    # Check for new comments
    CURRENT_COMMENT_COUNT=$(check_comments $PR_NUMBER 2>/dev/null || echo "0")

    if [ "$CURRENT_COMMENT_COUNT" -gt "$LAST_COMMENT_COUNT" ]; then
        echo ""
        echo "🔔 NEW COMMENT DETECTED!"
        echo "📊 Comment count: ${LAST_COMMENT_COUNT} → ${CURRENT_COMMENT_COUNT}"
        echo "📝 Latest comments:"
        get_latest_comments $PR_NUMBER 2>/dev/null || echo "Unable to fetch comment details"
        echo ""
        echo "⚡ Action required: Review and respond to feedback"
        echo ""

        LAST_COMMENT_COUNT=$CURRENT_COMMENT_COUNT
    fi

    # Sleep for the specified interval
    sleep $CHECK_INTERVAL
done

echo ""
echo "✅ Monitoring completed after ${DURATION_MINUTES} minutes"
echo "🔍 Total checks performed: ${COUNTER}"
echo "📊 Final comment count: ${CURRENT_COMMENT_COUNT}"
