#!/bin/bash

# Authentication testing script
# Tests various authentication scenarios

set -e

BASE_URL=${1:-"http://localhost:3000"}

echo "🔐 Testing authentication for SalesFlow AI"
echo "🌐 Base URL: $BASE_URL"
echo ""

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo "🧪 Testing: $description"
    echo "   Endpoint: $endpoint"
    
    # Make request and capture status code
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint" || echo "000")
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "   ✅ Status: $status_code (Expected: $expected_status)"
    else
        echo "   ❌ Status: $status_code (Expected: $expected_status)"
    fi
    echo ""
}

# Function to test page accessibility
test_page() {
    local path=$1
    local description=$2
    
    echo "🌐 Testing: $description"
    echo "   Path: $path"
    
    # Check if page is accessible
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$path" || echo "000")
    
    if [ "$status_code" = "200" ]; then
        echo "   ✅ Accessible (Status: $status_code)"
    else
        echo "   ⚠️  Status: $status_code"
    fi
    echo ""
}

echo "📄 Testing Public Pages"
echo "========================"
test_page "/" "Home page"
test_page "/login" "Login page"
test_page "/unauthorized" "Unauthorized page"

echo "🔒 Testing Protected API Endpoints (Should return 401)"
echo "======================================================"
test_api "/api/chat" "401" "Chat API (should be protected)"
test_api "/api/config" "401" "Config API (should be protected)"
test_api "/api/llm" "401" "LLM API (should be protected)"
test_api "/api/whatsapp/status" "401" "WhatsApp Status API (should be protected)"

echo "🌐 Testing Public API Endpoints (Should be accessible)"
echo "====================================================="
test_api "/api/auth/session" "200" "NextAuth session endpoint (should be public)"
test_api "/api/auth/providers" "200" "NextAuth providers endpoint (should be public)"

echo "🔍 Testing Protected Pages (Should redirect to login)"
echo "===================================================="
# Note: These will likely return 307/302 (redirect) or 200 if middleware redirects properly
test_page "/builder" "Builder page (should redirect if not authenticated)"
test_page "/assistant" "Assistant page (should redirect if not authenticated)"

echo "📊 Authentication Test Summary"
echo "=============================="
echo "✅ Public pages should be accessible (200)"
echo "🔒 Protected APIs should return 401 Unauthorized"
echo "🌐 Public APIs should return 200"
echo "🔄 Protected pages should redirect (307/302) or show login"
echo ""
echo "💡 To test authenticated scenarios:"
echo "   1. Login through the web interface"
echo "   2. Use browser dev tools to test API calls with session cookies"
echo "   3. Verify protected pages are accessible after login"
echo ""
