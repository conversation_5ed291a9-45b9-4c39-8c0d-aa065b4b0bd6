#!/bin/bash

# Development environment setup script
# Sets up the project for local development

set -e

echo "🚀 Setting up SalesFlow AI development environment..."
echo ""

# Check Node.js version
echo "📦 Checking Node.js version..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js version: $NODE_VERSION"
    
    # Check if version is 18 or higher
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -lt 18 ]; then
        echo "⚠️  Warning: Node.js 18+ is recommended. Current version: $NODE_VERSION"
    fi
else
    echo "❌ Node.js not found. Please install Node.js 18+ first."
    exit 1
fi

# Check package manager
echo ""
echo "📦 Checking package manager..."
if command -v pnpm &> /dev/null; then
    PACKAGE_MANAGER="pnpm"
    echo "✅ Using pnpm"
elif command -v yarn &> /dev/null; then
    PACKAGE_MANAGER="yarn"
    echo "✅ Using yarn"
elif command -v npm &> /dev/null; then
    PACKAGE_MANAGER="npm"
    echo "✅ Using npm"
else
    echo "❌ No package manager found. Please install npm, yarn, or pnpm."
    exit 1
fi

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
$PACKAGE_MANAGER install

# Check for environment file
echo ""
echo "🔧 Checking environment configuration..."
if [ ! -f ".env.local" ]; then
    if [ -f ".env.sample" ]; then
        echo "📋 Creating .env.local from .env.sample..."
        cp .env.sample .env.local
        echo "⚠️  Please edit .env.local with your actual values:"
        echo "   - NEXTAUTH_SECRET (generate with: openssl rand -base64 32)"
        echo "   - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET"
        echo "   - ALLOWED_EMAILS (comma-separated list)"
    else
        echo "⚠️  No .env.local or .env.sample found."
        echo "   Please create .env.local with required environment variables."
    fi
else
    echo "✅ .env.local exists"
fi

# Check Git hooks
echo ""
echo "🔗 Setting up Git hooks..."
if [ -d ".git" ]; then
    # Make scripts executable
    chmod +x scripts/*.sh
    echo "✅ Made scripts executable"
else
    echo "⚠️  Not a Git repository"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📝 Next steps:"
echo "   1. Edit .env.local with your configuration"
echo "   2. Run: $PACKAGE_MANAGER run dev"
echo "   3. Open: http://localhost:3000"
echo ""
echo "🔧 Available scripts:"
echo "   - $PACKAGE_MANAGER run dev     # Start development server"
echo "   - $PACKAGE_MANAGER run build   # Build for production"
echo "   - $PACKAGE_MANAGER run lint    # Run linting"
echo "   - ./scripts/monitor_pr.sh      # Monitor PR comments"
echo ""
