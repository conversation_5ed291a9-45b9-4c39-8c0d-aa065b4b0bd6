#!/bin/bash

# Enhanced Docker test script for SalesFlow AI
# Comprehensive testing including performance, security, and functionality tests

set -e

# Configuration
IMAGE_NAME="salesflow-ai"
TAG=${1:-"latest"}
FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
CONTAINER_NAME="salesflow-ai-test-enhanced"
TEST_PORT="3001"
WEBSOCKET_PORT="3002"
TIMEOUT=30
VERBOSE=${VERBOSE:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_section() {
    echo -e "\n${BLUE}🔍 $1${NC}"
    echo "=================================================="
}

# Cleanup function
cleanup() {
    log_info "Cleaning up test environment..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    rm -f .env.test
    log_success "Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT

# Test functions
test_image_exists() {
    log_section "Testing Docker Image Availability"

    if ! docker images "$FULL_IMAGE_NAME" --format "{{.Repository}}:{{.Tag}}" | grep -q "$FULL_IMAGE_NAME"; then
        log_error "Docker image $FULL_IMAGE_NAME not found"
        log_info "Run: ./scripts/docker-build.sh $TAG"
        exit 1
    fi

    # Get image info
    local image_size=$(docker images "$FULL_IMAGE_NAME" --format "{{.Size}}")
    local image_id=$(docker images "$FULL_IMAGE_NAME" --format "{{.ID}}")

    log_success "Image found: $FULL_IMAGE_NAME"
    log_info "Image ID: $image_id"
    log_info "Image Size: $image_size"
}

test_environment_setup() {
    log_section "Checking Environment Configuration"

    if [ ! -f ".env.local" ]; then
        log_error ".env.local file not found"
        log_info "Please create .env.local with your environment variables"
        exit 1
    fi

    log_success "Using existing .env.local file"
    log_info "Environment file found and will be used for testing"
}

start_container() {
    log_section "Starting Docker Container"

    # Stop existing container
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true

    # Start new container
    log_info "Starting container: $CONTAINER_NAME"
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$TEST_PORT:$TEST_PORT" \
        -p "$WEBSOCKET_PORT:$WEBSOCKET_PORT" \
        --env-file .env.test \
        --health-interval=10s \
        --health-timeout=5s \
        --health-retries=3 \
        "$FULL_IMAGE_NAME"

    log_success "Container started successfully"
}

wait_for_container() {
    log_section "Waiting for Container to be Ready"

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" | grep -q "$CONTAINER_NAME"; then
            log_info "Container is running (attempt $attempt/$max_attempts)"

            # Check health status
            local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$CONTAINER_NAME" 2>/dev/null || echo "unknown")
            log_info "Health status: $health_status"

            if [ "$health_status" = "healthy" ]; then
                log_success "Container is healthy and ready"
                return 0
            fi
        fi

        sleep 2
        ((attempt++))
    done

    log_error "Container failed to become ready within $((max_attempts * 2)) seconds"
    log_info "Container logs:"
    docker logs "$CONTAINER_NAME" --tail 20
    return 1
}

test_health_endpoint() {
    log_section "Testing Health Endpoint"

    local url="http://localhost:$TEST_PORT/api/health"
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" 2>/dev/null || echo "HTTPSTATUS:000")
    local body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    local status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')

    if [ "$status" = "200" ]; then
        log_success "Health endpoint responding correctly (HTTP $status)"

        if [ "$VERBOSE" = "true" ]; then
            log_info "Health response:"
            echo "$body" | jq . 2>/dev/null || echo "$body"
        fi
    else
        log_error "Health endpoint failed (HTTP $status)"
        log_info "Response: $body"
        return 1
    fi
}

test_main_endpoints() {
    log_section "Testing Main Application Endpoints"

    local endpoints=(
        "/"
        "/login"
        "/unauthorized"
        "/api/auth/providers"
    )

    for endpoint in "${endpoints[@]}"; do
        local url="http://localhost:$TEST_PORT$endpoint"
        local status=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")

        if [ "$status" = "200" ] || [ "$status" = "302" ] || [ "$status" = "401" ]; then
            log_success "Endpoint $endpoint: HTTP $status"
        else
            log_warning "Endpoint $endpoint: HTTP $status (might be expected)"
        fi
    done
}

test_container_performance() {
    log_section "Testing Container Performance"

    # Get container stats
    local stats=$(docker stats "$CONTAINER_NAME" --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}")
    log_info "Container resource usage:"
    echo "$stats"

    # Memory usage check
    local mem_usage=$(docker stats "$CONTAINER_NAME" --no-stream --format "{{.MemPerc}}" | sed 's/%//')
    if (( $(echo "$mem_usage > 80" | bc -l) )); then
        log_warning "High memory usage: ${mem_usage}%"
    else
        log_success "Memory usage is acceptable: ${mem_usage}%"
    fi
}

test_container_security() {
    log_section "Testing Container Security"

    # Check if running as non-root
    local user=$(docker exec "$CONTAINER_NAME" whoami 2>/dev/null || echo "unknown")
    if [ "$user" = "nextjs" ]; then
        log_success "Container running as non-root user: $user"
    else
        log_warning "Container user: $user (expected: nextjs)"
    fi

    # Check file permissions
    local permissions=$(docker exec "$CONTAINER_NAME" ls -la /app 2>/dev/null | head -5)
    log_info "Application directory permissions:"
    echo "$permissions"
}

show_container_info() {
    log_section "Container Information"

    echo "Container Status:"
    docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Size}}"

    echo -e "\nContainer Details:"
    docker inspect "$CONTAINER_NAME" --format "{{json .Config.Env}}" | jq -r '.[]' | grep -E "(NODE_ENV|PORT|HOSTNAME)" || true

    echo -e "\nTest URLs:"
    echo "🌐 Main Application: http://localhost:$TEST_PORT"
    echo "🔍 Health Check: http://localhost:$TEST_PORT/api/health"
    echo "🔐 Login: http://localhost:$TEST_PORT/login"
    echo "📊 WebSocket Port: $WEBSOCKET_PORT"
}

# Main execution
main() {
    echo "🧪 Enhanced Docker Testing for SalesFlow AI"
    echo "Image: $FULL_IMAGE_NAME"
    echo "Container: $CONTAINER_NAME"
    echo "Test Port: $TEST_PORT"
    echo ""

    test_image_exists
    test_environment_setup
    start_container
    wait_for_container
    test_health_endpoint
    test_main_endpoints
    test_container_performance
    test_container_security
    show_container_info

    log_success "All tests completed successfully!"
    log_info "Container is running and ready for manual testing"
    log_info "Run 'docker logs $CONTAINER_NAME' to view application logs"
    log_info "Run 'docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME' to cleanup"
}

# Run main function
main "$@"
