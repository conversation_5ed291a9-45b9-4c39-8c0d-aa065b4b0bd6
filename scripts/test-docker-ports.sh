#!/bin/bash

# Test script to verify Docker container ports are working correctly
# Tests both the Next.js app (port 3000) and WebSocket server (port 3002)

set -e

echo "🧪 Testing Docker container ports..."
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test Next.js app on port 3000
echo "📱 Testing Next.js app on port 3000..."
if curl -s -f http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ Next.js app is accessible on port 3000${NC}"
else
    echo -e "${RED}❌ Next.js app is NOT accessible on port 3000${NC}"
    exit 1
fi

# Test WebSocket server on port 3002
echo ""
echo "🔌 Testing WebSocket server on port 3002..."
if nc -z localhost 3002 2>/dev/null; then
    echo -e "${GREEN}✅ WebSocket server is listening on port 3002${NC}"
else
    echo -e "${RED}❌ WebSocket server is NOT listening on port 3002${NC}"
    exit 1
fi

# Test WebSocket connection (requires wscat if available)
echo ""
echo "🌐 Testing WebSocket connection..."
if command -v wscat &> /dev/null; then
    echo "Testing WebSocket connection with wscat..."
    timeout 5 wscat -c ws://localhost:3002 -x '{"type":"ping"}' 2>/dev/null && \
        echo -e "${GREEN}✅ WebSocket connection successful${NC}" || \
        echo -e "${YELLOW}⚠️  WebSocket connection test inconclusive (this is normal)${NC}"
else
    echo -e "${YELLOW}⚠️  wscat not available, skipping WebSocket connection test${NC}"
    echo "   Install wscat with: npm install -g wscat"
fi

echo ""
echo -e "${GREEN}🎉 All port tests completed successfully!${NC}"
echo ""
echo "📋 Summary:"
echo "   • Next.js app: http://localhost:3000"
echo "   • WebSocket server: ws://localhost:3002"
echo "   • Container is ready for use"
