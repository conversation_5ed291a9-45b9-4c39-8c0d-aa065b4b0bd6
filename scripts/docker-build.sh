#!/bin/bash

# Docker build script for SalesFlow AI
# Builds optimized Docker image for production deployment

set -e

# Configuration
IMAGE_NAME="salesflow-ai"
TAG=${1:-"latest"}
FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"

echo "🐳 Building Docker image: $FULL_IMAGE_NAME"
echo "📁 Build context: $(pwd)"
echo ""

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    echo "❌ Dockerfile not found in current directory"
    exit 1
fi

# Build the Docker image
echo "🔨 Building Docker image..."
docker build \
    --tag "$FULL_IMAGE_NAME" \
    --build-arg NODE_ENV=production \
    --build-arg NEXT_TELEMETRY_DISABLED=1 \
    .

# Check build success
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Docker image built successfully!"
    echo "📦 Image: $FULL_IMAGE_NAME"
    
    # Show image size
    echo ""
    echo "📊 Image information:"
    docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo ""
    echo "🚀 To run the container:"
    echo "   docker run -p 3000:3000 --env-file .env.local $FULL_IMAGE_NAME"
    echo ""
    echo "🔍 To test the container:"
    echo "   ./scripts/docker-test.sh $TAG"
    
else
    echo ""
    echo "❌ Docker build failed!"
    exit 1
fi
