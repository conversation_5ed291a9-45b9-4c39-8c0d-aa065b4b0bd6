#!/bin/bash

# Docker test script for SalesFlow AI
# Tests the built Docker image locally using .env.local configuration

set -e

# Configuration
IMAGE_NAME="salesflow-ai"
TAG=${1:-"latest"}
FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
CONTAINER_NAME="salesflow-ai-test"
TEST_PORT="3001"

echo "🧪 Testing Docker image: $FULL_IMAGE_NAME"
echo ""

# Check if image exists
if ! docker images "$FULL_IMAGE_NAME" --format "{{.Repository}}:{{.Tag}}" | grep -q "$FULL_IMAGE_NAME"; then
    echo "❌ Docker image $FULL_IMAGE_NAME not found"
    echo "💡 Run: ./scripts/docker-build.sh $TAG"
    exit 1
fi

# Stop and remove existing test container
echo "🧹 Cleaning up existing test container..."
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# Check if .env.local exists
echo "📝 Checking for .env.local..."
if [ ! -f ".env.local" ]; then
    echo "❌ .env.local file not found"
    echo "💡 Please create .env.local with your environment variables"
    exit 1
fi

# Create temporary test environment file based on .env.local
echo "📝 Creating test environment from .env.local..."
cp .env.local .env.test

# Override specific variables for testing
echo "" >> .env.test
echo "# Test overrides" >> .env.test
echo "NODE_ENV=production" >> .env.test
echo "PORT=$TEST_PORT" >> .env.test
echo "HOSTNAME=0.0.0.0" >> .env.test
echo "NEXTAUTH_URL=http://localhost:$TEST_PORT" >> .env.test

echo "✅ Test environment prepared"

# Run the container
echo "🚀 Starting test container..."
docker run -d \
    --name "$CONTAINER_NAME" \
    -p "$TEST_PORT:$TEST_PORT" \
    --env-file .env.test \
    "$FULL_IMAGE_NAME"

# Wait for container to start
echo "⏳ Waiting for container to start..."
sleep 10

# Test health endpoint
echo "🔍 Testing health endpoint..."
if curl -f "http://localhost:$TEST_PORT/api/health" > /dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    echo "📋 Container logs:"
    docker logs "$CONTAINER_NAME"

    # Cleanup
    docker stop "$CONTAINER_NAME"
    docker rm "$CONTAINER_NAME"
    rm -f .env.test
    exit 1
fi

# Test main page
echo "🌐 Testing main page..."
if curl -f "http://localhost:$TEST_PORT/" > /dev/null 2>&1; then
    echo "✅ Main page accessible!"
else
    echo "⚠️  Main page test failed (might be due to authentication)"
fi

# Show container info
echo ""
echo "📊 Container information:"
docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "✅ Docker test completed successfully!"
echo "🌐 Test URL: http://localhost:$TEST_PORT"
echo "🔍 Health check: http://localhost:$TEST_PORT/api/health"
echo ""
echo "🧹 To cleanup:"
echo "   docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME"
echo ""
echo "📋 To view logs:"
echo "   docker logs $CONTAINER_NAME"

# Cleanup test environment file
rm -f .env.test
