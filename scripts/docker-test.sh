#!/bin/bash

# Docker test script for SalesFlow AI
# Tests the built Docker image locally using .env.local configuration

set -e

# Configuration
IMAGE_NAME="salesflow-ai"
TAG=${1:-"latest"}
FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
CONTAINER_NAME="salesflow-ai-test"
TEST_PORT="3001"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_section() {
    echo -e "\n${BLUE}🔍 $1${NC}"
    echo "=================================================="
}

# Cleanup function
cleanup() {
    log_info "Cleaning up test environment..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    log_success "Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT

echo "🧪 Testing Docker image: $FULL_IMAGE_NAME"
echo "Container: $CONTAINER_NAME"
echo "Test Port: $TEST_PORT"
echo ""

# Test 1: Check if image exists
log_section "Testing Docker Image Availability"

if ! docker images "$FULL_IMAGE_NAME" --format "{{.Repository}}:{{.Tag}}" | grep -q "$FULL_IMAGE_NAME"; then
    log_error "Docker image $FULL_IMAGE_NAME not found"
    log_info "Run: ./scripts/docker-build.sh $TAG"
    exit 1
fi

# Get image info
image_size=$(docker images "$FULL_IMAGE_NAME" --format "{{.Size}}")
image_id=$(docker images "$FULL_IMAGE_NAME" --format "{{.ID}}")

log_success "Image found: $FULL_IMAGE_NAME"
log_info "Image ID: $image_id"
log_info "Image Size: $image_size"

# Test 2: Check environment file
log_section "Checking Environment Configuration"

if [ ! -f ".env.local" ]; then
    log_error ".env.local file not found"
    log_info "Please create .env.local with your environment variables"
    exit 1
fi

log_success "Using existing .env.local file"
log_info "Environment file found and will be used for testing"

# Test 3: Start container
log_section "Starting Docker Container"

# Stop and remove existing container first
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

log_info "Starting container: $CONTAINER_NAME"
docker run -d \
    --name "$CONTAINER_NAME" \
    -p "$TEST_PORT:$TEST_PORT" \
    -e "PORT=$TEST_PORT" \
    -e "HOSTNAME=0.0.0.0" \
    -e "NEXTAUTH_URL=http://localhost:$TEST_PORT" \
    --env-file .env.local \
    "$FULL_IMAGE_NAME"

log_success "Container started successfully"

# Test 4: Wait for container to be ready
log_section "Waiting for Container to be Ready"

max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker ps --filter "name=$CONTAINER_NAME" --filter "status=running" | grep -q "$CONTAINER_NAME"; then
        log_info "Container is running (attempt $attempt/$max_attempts)"

        # Try to connect to health endpoint
        if curl -f "http://localhost:$TEST_PORT/api/health" > /dev/null 2>&1; then
            log_success "Container is healthy and ready"
            break
        fi
    fi

    if [ $attempt -eq $max_attempts ]; then
        log_error "Container failed to become ready within $((max_attempts * 2)) seconds"
        log_info "Container logs:"
        docker logs "$CONTAINER_NAME" --tail 20
        exit 1
    fi

    sleep 2
    ((attempt++))
done

# Test 5: Test health endpoint
log_section "Testing Health Endpoint"

url="http://localhost:$TEST_PORT/api/health"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" 2>/dev/null || echo "HTTPSTATUS:000")
body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')

if [ "$status" = "200" ]; then
    log_success "Health endpoint responding correctly (HTTP $status)"
    log_info "Health response preview:"
    echo "$body" | head -3
else
    log_error "Health endpoint failed (HTTP $status)"
    log_info "Response: $body"
    exit 1
fi

# Test 6: Test main endpoints
log_section "Testing Main Application Endpoints"

endpoints=("/" "/login" "/unauthorized")

for endpoint in "${endpoints[@]}"; do
    url="http://localhost:$TEST_PORT$endpoint"
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")

    if [ "$status" = "200" ] || [ "$status" = "302" ] || [ "$status" = "401" ]; then
        log_success "Endpoint $endpoint: HTTP $status"
    else
        log_warning "Endpoint $endpoint: HTTP $status (might be expected)"
    fi
done

# Test 7: Show container info
log_section "Container Information"

echo "Container Status:"
docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo -e "\nTest URLs:"
echo "🌐 Main Application: http://localhost:$TEST_PORT"
echo "🔍 Health Check: http://localhost:$TEST_PORT/api/health"
echo "🔐 Login: http://localhost:$TEST_PORT/login"

log_success "All tests completed successfully!"
log_info "Container is running and ready for manual testing"
log_info "Run 'docker logs $CONTAINER_NAME' to view application logs"
log_info "Run 'docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME' to cleanup"

echo ""
echo "🎉 Docker container is working correctly!"
echo "You can now test the application manually."
