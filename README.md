# SalesFlow AI Builder

A Next.js application for building AI-powered sales flows with Google OAuth authentication, Supabase integration, and WhatsApp automation.

## 🚀 Features

- **Google OAuth Authentication** - Secure user authentication with NextAuth.js
- **Email Whitelist Security** - Restrict access to approved users only
- **Comprehensive API Protection** - All content APIs secured with middleware
- **Protected Routes** - Middleware-based route protection with unauthorized page
- **Modern UI** - Responsive design with Tailwind CSS and logout functionality
- **TypeScript** - Full type safety throughout the application
- **Database Ready** - Prepared for Supabase integration
- **AI Integration Ready** - Structured for OpenAI API integration
- **WhatsApp Automation** - Ready for Baileys WhatsApp integration

## 🛠️ Environment Setup

### Required Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Security Configuration - Email Access Control
# Comma-separated list of allowed email addresses (REQUIRED)
ALLOWED_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>

# Supabase Configuration (for future tasks)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# OpenAI Configuration (for future tasks)
OPENAI_API_KEY=your-openai-api-key

# WhatsApp/Baileys Configuration (for future tasks)
WHATSAPP_SESSION_PATH=./whatsapp-sessions
```

### 🔐 NEXTAUTH_SECRET Generation

The `NEXTAUTH_SECRET` is a critical security component. Generate a secure secret using one of these methods:

#### Option 1: OpenSSL (Recommended)

```bash
openssl rand -base64 32
```

#### Option 2: Node.js

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

#### Option 3: Online Generator

Use a secure random string generator (minimum 32 characters)

### 🔧 Google OAuth Setup

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create a new project** or select existing project
3. **Enable Google+ API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`
5. **Copy credentials** to your `.env.local` file

### 🌍 Environment-Specific Configuration

#### Development Environment

```env
NEXTAUTH_URL=http://localhost:3000
```

#### Staging Environment

```env
NEXTAUTH_URL=https://your-staging-domain.com
```

#### Production Environment

```env
NEXTAUTH_URL=https://your-production-domain.com
```

**Important**:

- Use different `NEXTAUTH_SECRET` values for each environment
- Update Google OAuth redirect URIs for each environment
- Never commit `.env.local` files to version control

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. **Clone the repository**

```bash
git clone https://github.com/anchorsprint/sales-flow-ai.git
cd sales-flow-ai
```

2. **Install dependencies**

```bash
npm install
```

3. **Set up environment variables**

```bash
cp .env.sample .env.local
# Edit .env.local with your actual values
```

4. **Run the development server**

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

5. **Open your browser**

Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 🧪 Testing Authentication

### Successful Authentication Flow

1. **Visit the home page** - Should show "Sign In" button when not authenticated
2. **Click "Get Started"** - Should redirect to `/login` page
3. **Click "Continue with Google"** - Should redirect to Google OAuth consent screen
4. **Complete authentication with allowed email** - Should redirect back and show authenticated state
5. **Test protected routes** - Visit `/builder` to verify route protection works
6. **Test logout** - Use logout button on protected pages

### Unauthorized Access Testing

1. **Try logging in with non-whitelisted email** - Should redirect to `/unauthorized` page
2. **Visit protected routes without authentication** - Should redirect to `/login`
3. **Test API endpoints without authentication** - Should return 401 Unauthorized
4. **Verify unauthorized page accessibility** - Should be accessible without authentication

### API Security Testing

Test API endpoints to verify protection:

```bash
# Should return 401 Unauthorized (no session)
curl http://localhost:3000/api/chat

# Should return 401 Unauthorized (no session)
curl http://localhost:3000/api/config

# Should work (public route)
curl http://localhost:3000/api/auth/session
```

## 📁 Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── auth/[...nextauth]/route.ts  # NextAuth API endpoint with email whitelist
│   │   ├── chat/route.ts                # Protected AI chat API
│   │   ├── config/route.ts              # Protected configuration API
│   │   ├── llm/route.ts                 # Protected LLM API
│   │   ├── assistant/*/route.ts         # Protected assistant management APIs
│   │   ├── whatsapp/*/route.ts          # Protected WhatsApp automation APIs
│   │   └── test-db/route.ts             # Protected database testing API
│   ├── login/page.tsx                   # Login page with Google OAuth
│   ├── unauthorized/page.tsx            # Unauthorized access page (public)
│   ├── builder/page.tsx                 # Protected builder page with logout
│   ├── assistant/page.tsx               # Protected assistant page with logout
│   ├── layout.tsx                       # Root layout with SessionProvider
│   └── page.tsx                         # Home page with auth-aware navigation
├── components/
│   ├── providers/SessionProvider.tsx    # NextAuth session wrapper
│   └── ui/UserDropdown.tsx              # User menu with logout functionality
├── hooks/
│   └── useAuth.ts                       # Authentication hook
├── lib/
│   ├── auth/
│   │   ├── index.ts                     # Server-side auth utilities
│   │   └── withAuth.ts                  # Higher-order function for API protection
│   └── index.ts                         # Library exports
├── types/
│   └── index.ts                         # TypeScript type definitions
└── middleware.ts                        # Comprehensive route protection middleware
```

## 🔒 Security Features

- **JWT-based sessions** with secure token management
- **Comprehensive API Protection** - All content APIs secured with middleware
- **Email whitelist access control** - Restrict login to approved emails only
- **Unauthorized page handling** - Proper error pages for rejected logins
- **Environment variable validation**
- **Secure secret generation** for production deployments
- **OAuth 2.0 compliance** with Google authentication
- **Row-level security** ready for Supabase integration

### Email Access Control

The application includes simplified email-based access control to restrict who can sign in:

#### Email Whitelist Configuration

Add specific email addresses to the `ALLOWED_EMAILS` environment variable:

```env
ALLOWED_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>
```

#### Security Behavior

- **Simple email-only whitelist** - No domain-based access for maximum security
- Users not in the whitelist will be **redirected to unauthorized page**
- Rejected sign-ins are logged for security monitoring
- Email matching is case-insensitive
- If `ALLOWED_EMAILS` is empty, **no one can sign in** (fail-secure)
- Unauthorized page is publicly accessible for proper error handling

### API Security

All content APIs are automatically protected by middleware:

#### Protected API Routes

- `/api/chat` - AI chat completions with user configurations
- `/api/config` - User configuration management
- `/api/llm` - LLM API with user context
- `/api/assistant/*` - Assistant management endpoints
- `/api/whatsapp/*` - WhatsApp automation endpoints (except webhooks)
- `/api/test-db` - Database testing endpoints

#### Public API Routes

- `/api/auth/*` - NextAuth authentication endpoints
- Root pages: `/`, `/login`, `/unauthorized`

#### API Security Standards

- **Next.js Middleware Protection** - Using `withAuth` from NextAuth.js for proper route protection
- **Centralized authentication** - All protection logic in `src/middleware.ts`
- **Pattern-based routing** - Flexible regex patterns for public/protected route matching
- **No redundant auth checks** - Clean, maintainable code without repetitive authentication
- **Consistent error handling** - Standardized 401 responses for unauthorized access
- **Type-safe authentication** - Proper session handling with TypeScript support

#### Next.js Middleware Implementation

Our middleware follows Next.js best practices:

```typescript
// src/middleware.ts
import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware() {
    // Additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Define public routes
        const publicRoutes = ['/', '/login', '/unauthorized'];
        const publicApiPatterns = [/^\/api\/auth\//];

        // Check if route is public
        if (
          publicRoutes.includes(pathname) ||
          publicApiPatterns.some(pattern => pattern.test(pathname))
        ) {
          return true;
        }

        // Protected routes require valid token
        return !!token;
      },
    },
  }
);

// Matcher configuration for optimal performance
export const config = {
  matcher: [
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
```

**Benefits of Next.js Middleware Approach:**

- ✅ **Edge Runtime** - Runs at the edge for better performance
- ✅ **Request Interception** - Catches requests before they reach API routes
- ✅ **Automatic Redirects** - Built-in redirect handling for unauthorized access
- ✅ **Pattern Matching** - Efficient route matching with regex support
- ✅ **NextAuth Integration** - Seamless integration with NextAuth.js

#### Middleware Best Practices

Our implementation follows Next.js middleware best practices:

1. **Efficient Matcher Configuration**

   - Excludes static files and Next.js internals for optimal performance
   - Uses negative lookahead regex for precise route matching
   - Prevents unnecessary middleware execution on static assets

2. **Centralized Route Logic**

   - All authentication logic in one place (`src/middleware.ts`)
   - Easy to maintain and update security rules
   - Consistent behavior across the entire application

3. **Performance Optimized**

   - Runs on Edge Runtime for faster execution
   - Minimal processing overhead
   - Early return for public routes

4. **Security First**
   - Fail-secure approach - requires explicit public route definition
   - Token-based authentication validation
   - Proper redirect handling for unauthorized access

## 🚀 Deployment

### Environment Variables for Production

When deploying to production, ensure you have:

1. **Generated a new NEXTAUTH_SECRET** for production
2. **Updated NEXTAUTH_URL** to your production domain
3. **Added production domain** to Google OAuth redirect URIs
4. **Set all required environment variables** in your deployment platform

### Vercel Deployment

1. **Connect your repository** to Vercel
2. **Add environment variables** in Vercel dashboard
3. **Update Google OAuth settings** with production URLs
4. **Deploy** - Vercel will automatically build and deploy

### Other Platforms

For other deployment platforms (Netlify, Railway, etc.):

1. **Set environment variables** in platform dashboard
2. **Configure build settings** (usually automatic for Next.js)
3. **Update OAuth redirect URIs** for your domain
4. **Deploy** following platform-specific instructions

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

### Code Quality

- **ESLint** - Code linting and formatting
- **TypeScript** - Type safety and better developer experience
- **Prettier** - Code formatting (configured in ESLint)

## 📚 Learn More

### Next.js Resources

- [Next.js Documentation](https://nextjs.org/docs) - Learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn) - Interactive Next.js tutorial

### Authentication Resources

- [NextAuth.js Documentation](https://next-auth.js.org/) - Authentication for Next.js
- [Google OAuth 2.0](https://developers.google.com/identity/protocols/oauth2) - Google OAuth documentation

### Deployment Resources

- [Next.js Deployment](https://nextjs.org/docs/app/building-your-application/deploying) - Deployment documentation
- [Vercel Platform](https://vercel.com/new) - Deploy Next.js apps

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
